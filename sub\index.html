<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://secure-notes-api.v8x.workers.dev; img-src 'self' data:; font-src 'self'">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <title>SecureNotes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .access-gate {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(20px);
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .lock-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input[type="password"], input[type="text"], input[type="email"], textarea {
            width: 100%;
            padding: 14px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        /* Password input container with eye icon */
        .password-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-container input[type="password"],
        .password-container input[type="text"] {
            padding-right: 45px; /* Make room for eye icon */
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 18px;
            color: #666;
            user-select: none;
            transition: color 0.3s;
        }

        .password-toggle:hover {
            color: #333;
        }

        /* Password strength indicator */
        .password-hint {
            margin-top: 8px;
            padding: 12px;
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.4;
        }

        .password-hint.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .password-hint.danger {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .password-hint.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .password-requirements {
            margin-top: 5px;
            font-size: 13px;
        }

        .password-requirements ul {
            margin: 5px 0;
            padding-left: 20px;
        }

        .password-requirements li {
            margin: 2px 0;
        }

        .requirement-met {
            color: #28a745;
        }

        .requirement-unmet {
            color: #dc3545;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 32px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Loading spinner styles */
        .btn.loading {
            position: relative;
            color: transparent;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Icon Button Styles */
        .btn-icon {
            width: auto;
            min-width: 44px;
            height: 44px;
            padding: 8px 12px;
            margin-bottom: 0;
            margin-left: 8px;
            font-size: 18px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            position: relative;
        }

        .btn-icon:hover {
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-icon:first-child {
            margin-left: 0;
        }

        /* Tooltip styles for icon buttons */
        .btn-icon[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: normal;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
        }

        .btn-icon[title]:hover::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-bottom-color: rgba(0, 0, 0, 0.8);
            z-index: 1001;
            pointer-events: none;
        }

        /* Admin Styles */
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .btn-admin:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .btn-admin.btn-icon:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .admin-section {
            display: none;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .admin-panel {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .admin-panel h4 {
            margin-bottom: 15px;
            color: #495057;
            font-weight: 600;
        }

        .backup-controls, .user-controls {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .backup-controls .btn {
            flex: 1;
            min-width: 140px;
        }

        .status-display {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: white;
            border: 1px solid #e9ecef;
            min-height: 50px;
        }

        .users-list {
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            margin: 8px 0;
            border-radius: 6px;
            background: white;
            transition: all 0.2s ease;
        }

        .user-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .user-disabled {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            opacity: 0.8;
        }

        .user-info {
            flex: 1;
        }

        .user-email {
            font-weight: 600;
            color: #495057;
        }

        .user-meta {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .toggle-user-btn {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .toggle-user-btn.enable {
            background: #28a745;
            color: white;
        }

        .toggle-user-btn.disable {
            background: #dc3545;
            color: white;
        }

        .toggle-user-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-user-btn:disabled {
            background: #6c757d !important;
            color: #fff !important;
            cursor: not-allowed !important;
            opacity: 0.6;
            transform: none !important;
            box-shadow: none !important;
        }

        .toggle-user-btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Loading state for toggle-user-btn */
        .toggle-user-btn.loading {
            color: transparent !important;
            pointer-events: none;
        }

        .toggle-user-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .user-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .user-status.active {
            background: #d4edda;
            color: #155724;
        }

        .user-status.disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            padding: 15px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }

        /* Progress bar styles */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: rgba(102, 126, 234, 0.2);
            z-index: 9999;
            display: none;
        }

        .progress-bar.active {
            display: block;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            animation: progress 2s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .btn-secondary {
            background: #6c757d;
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-danger {
            background: #dc3545;
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            margin-right: 5px;
            margin-bottom: 0;
            width: auto;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .modal-header h3 {
            margin: 0;
            color: #495057;
            font-size: 18px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            padding: 25px;
            max-height: 50vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background: #f8f9fa;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }

        /* 2FA Styles */
        .twofa-status-container {
            margin-top: 15px;
        }

        .twofa-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-indicator.enabled {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-indicator.disabled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-indicator.checking {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .twofa-setup-steps {
            margin-bottom: 20px;
        }

        .step-item {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .step-item:last-child {
            border-bottom: none;
        }

        .step-item h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
        }

        .secret-manual {
            margin-top: 20px;
        }

        .secret-code {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 10px;
        }

        .secret-code input {
            flex: 1;
            font-family: monospace;
            font-size: 14px;
            background-color: #f8f9fa;
        }

        .backup-code-option {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .backup-files-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .backup-file-item {
            padding: 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .backup-file-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .backup-file-item.selected {
            border-color: #667eea;
            background: #e7f3ff;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .backup-file-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .backup-file-details {
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
        }

        .error {
            color: #dc3545;
            margin-top: 10px;
            font-size: 14px;
        }

        .main-app {
            display: none;
            width: 100%;
            min-height: 100vh;
        }

        .main-app.show {
            display: block;
        }

        /* Main application styles */
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            color: white;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .auth-section {
            margin-bottom: 20px;
        }

        .auth-form {
            display: block;
        }

        .auth-form.hidden {
            display: none;
        }

        .auth-toggle {
            text-align: center;
            margin-top: 15px;
        }

        .auth-toggle a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
        }

        .auth-toggle a:hover {
            text-decoration: underline;
        }

        .auth-form h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .status {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .notes-section {
            display: none;
        }

        .notes-section.active {
            display: block;
        }

        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .notes-header h3 {
            color: #333;
            font-size: 1.5rem;
        }

        .header-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 15px;
        }

        .settings-group {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .settings-group h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }

        .settings-description {
            margin-bottom: 20px;
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
        }

        .info-item label {
            font-weight: bold;
            color: #555;
        }

        .info-item span {
            color: #333;
        }

        .note-controls {
            display: flex;
            gap: 10px;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .note-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #667eea;
            position: relative;
        }

        .note-item.edit-mode {
            background: #fff3cd;
            border-left-color: #ffc107;
            padding: 12px 16px;
        }

        .note-item.edit-mode .note-actions {
            margin-bottom: 8px;
        }

        .edit-textarea {
            width: 100%;
            min-height: 150px;
            max-height: 300px;
            padding: 12px;
            border: 2px solid #ffc107;
            border-radius: 6px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            margin-top: 8px;
            margin-right: 0;
            box-sizing: border-box;
        }

        .edit-actions {
            display: flex;
            gap: 10px;
            margin-top: 8px;
            justify-content: flex-end;
            align-items: center;
        }

        .note-actions {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            gap: 5px;
        }

        .note-meta {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .note-content {
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
            margin-right: 80px;
        }

        .note-item.edit-mode .note-content {
            margin-right: 0;
        }

        .note-content.collapsed {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .note-expand-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
            padding: 4px 8px;
            text-decoration: underline;
            align-self: flex-start;
        }

        .note-expand-btn:hover {
            color: #5a6fd8;
        }

        .char-counter {
            position: absolute;
            bottom: 8px;
            right: 8px;
            font-size: 12px;
            color: #666;
            background: rgba(255, 255, 255, 0.9);
            padding: 2px 6px;
            border-radius: 3px;
            pointer-events: none;
        }

        .edit-char-counter {
            font-size: 12px;
            color: #666;
            margin-right: auto;
            padding: 2px 6px;
        }

        .edit-char-counter.warning {
            color: #ff6b35;
        }

        .edit-char-counter.error {
            color: #dc3545;
        }

        .char-counter.warning {
            color: #ff6b35;
        }

        .char-counter.error {
            color: #dc3545;
        }

        .form-group {
            position: relative;
        }

        .search-container {
            position: relative;
            display: inline-block;
            margin-left: 15px;
        }

        .search-container input {
            width: 200px;
            padding: 8px 30px 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .search-container input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-clear-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn:hover {
            color: #666;
        }

        .search-container input:not(:placeholder-shown) + .search-clear-btn {
            display: flex;
        }

        .search-section {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .search-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-scope-select {
            padding: 6px 8px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 12px;
            background: white;
            cursor: pointer;
            min-width: 90px;
        }

        .search-scope-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .crypto-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .crypto-info h3 {
            color: #0066cc;
            margin-bottom: 8px;
        }

        .crypto-info p {
            font-size: 14px;
            color: #0066cc;
            margin-bottom: 4px;
        }

        .edit-mode {
            background: #fff3cd;
            border-left-color: #ffc107;
        }

        .edit-textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
            margin-right: 0;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .auth-section {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2rem;
            }

            .note-content {
                margin-right: 0;
                margin-bottom: 40px;
            }

            .note-actions {
                position: static;
                margin-top: 10px;
                justify-content: flex-end;
            }

            .note-controls {
                flex-direction: column;
                gap: 10px;
            }

            .search-section {
                margin-left: 0;
                align-self: stretch;
                flex-direction: column;
                align-items: stretch;
            }

            .search-controls {
                flex-direction: column;
                gap: 8px;
            }

            .search-container {
                margin-left: 0;
            }

            .search-container input {
                width: 100%;
            }

            .search-scope-select {
                width: 100%;
                min-width: auto;
            }

            .notes-header {
                flex-direction: column;
                align-items: stretch;
            }

            .note-controls {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Progress bar for loading operations -->
    <div id="progress-bar" class="progress-bar"></div>

    <!-- Loading state -->
    <div id="loading-state" class="access-gate">
        <div class="lock-icon">⏳</div>
        <h1>Loading...</h1>
        <p class="subtitle">Checking access requirements...</p>
    </div>

    <!-- Access password verification layer -->
    <div id="access-gate" class="access-gate" style="display: none;">
        <div class="lock-icon">🔒</div>
        <h1>Access Verification</h1>
        <p class="subtitle">Please enter the site access password to continue</p>

        <div class="form-group">
            <label for="site-password">Access Password</label>
            <div class="password-container">
                <input type="password" id="site-password" placeholder="Enter access password" autocomplete="off">
                <span class="password-toggle" onclick="togglePasswordVisibility('site-password')">👁️</span>
            </div>
        </div>

        <button class="btn" id="verify-access-btn">Verify Access</button>
        <div id="access-error" class="error"></div>
    </div>
    <script>
        const API_BASE = 'https://secure-notes-api.v8x.workers.dev';

        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            if (!input) return;
            input.type = input.type === 'password' ? 'text' : 'password';
        }

        async function sha256(message) {
            const msgBuffer = new TextEncoder().encode(message);
            const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        function showLoading() {
            const loading = document.getElementById('loading-state');
            const gate = document.getElementById('access-gate');
            if (loading) loading.style.display = 'block';
            if (gate) gate.style.display = 'none';
        }

        function showGate() {
            const loading = document.getElementById('loading-state');
            const gate = document.getElementById('access-gate');
            if (loading) loading.style.display = 'none';
            if (gate) gate.style.display = 'block';
        }

        function redirectWithToken(token, page) {
            const target = page + '?accessToken=' + encodeURIComponent(token);
            window.location.replace(target);
        }

        async function checkAccessRequirement() {
            showLoading();
            try {
                const res = await fetch(API_BASE + '/api/check-access-requirement', { method: 'GET' });
                const data = await res.json().catch(() => ({}));
                if (res.ok && data && data.success && data.siteAccessRequired === false && data.accessToken) {
                    if (data.redirectPage && typeof data.redirectPage === 'string') {
                        redirectWithToken(data.accessToken, data.redirectPage);
                        return;
                    }
                }
                // Default: require access password
                showGate();
            } catch (e) {
                // Network issues: fall back to gate
                showGate();
            }
        }

        async function verifyAccess() {
            const btn = document.getElementById('verify-access-btn');
            const err = document.getElementById('access-error');
            const pwdInput = document.getElementById('site-password');
            if (err) err.textContent = '';

            const pwd = (pwdInput && pwdInput.value) ? pwdInput.value : '';
            if (!pwd) {
                if (err) err.textContent = 'Please enter a valid access password';
                return;
            }

            if (btn) {
                btn.disabled = true;
                btn.classList.add('loading');
            }

            try {
                const hash = await sha256(pwd);
                const res = await fetch(API_BASE + '/api/verify-access', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ hash })
                });
                const data = await res.json().catch(() => ({}));
                if (res.ok && data && data.success && data.accessToken) {
                    // Clear input quickly
                    if (pwdInput) pwdInput.value = '';
                    redirectWithToken(data.accessToken, data.redirectPage);
                } else {
                    if (err) err.textContent = (data && data.error) || 'Invalid access password, please try again';
                }
            } catch (e) {
                if (err) err.textContent = 'Verification failed, please try again';
            } finally {
                if (btn) {
                    btn.disabled = false;
                    btn.classList.remove('loading');
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            checkAccessRequirement();
            const btn = document.getElementById('verify-access-btn');
            if (btn) btn.addEventListener('click', verifyAccess);
            const pwdInput = document.getElementById('site-password');
            if (pwdInput) {
                pwdInput.addEventListener('keypress', function (e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        verifyAccess();
                    }
                });
            }
        });
    </script>
</body>
</html>