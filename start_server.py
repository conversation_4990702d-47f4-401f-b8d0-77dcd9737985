#!/usr/bin/env python3
"""
简单的HTTP服务器用于测试SecureNotes应用
运行在 localhost:8080
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

# 设置端口
PORT = 8080

# 设置服务目录为sub文件夹
SERVE_DIR = Path(__file__).parent / "sub"

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(SERVE_DIR), **kwargs)
    
    def end_headers(self):
        # 添加CORS头部以允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Access-Token')
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{self.log_date_time_string()}] {format % args}")

def main():
    # 检查sub目录是否存在
    if not SERVE_DIR.exists():
        print(f"错误: 目录 {SERVE_DIR} 不存在")
        sys.exit(1)
    
    # 切换到服务目录
    os.chdir(SERVE_DIR)
    
    # 创建服务器
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        print(f"服务器启动成功!")
        print(f"访问地址: http://localhost:{PORT}")
        print(f"服务目录: {SERVE_DIR}")
        print(f"测试页面: http://localhost:{PORT}/test_login.html")
        print(f"主页面: http://localhost:{PORT}/index.html")
        print("按 Ctrl+C 停止服务器")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == "__main__":
    main()
