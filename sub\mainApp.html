<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://secure-notes-api.v8x.workers.dev; img-src 'self' data:; font-src 'self'">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <script src="qrcode.min.js"></script>
    <title>SecureNotes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .access-gate {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(20px);
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .lock-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input[type="password"], input[type="text"], input[type="email"], textarea {
            width: 100%;
            padding: 14px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        /* Password input container with eye icon */
        .password-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-container input[type="password"],
        .password-container input[type="text"] {
            padding-right: 45px; /* Make room for eye icon */
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 18px;
            color: #666;
            user-select: none;
            transition: color 0.3s;
        }

        .password-toggle:hover {
            color: #333;
        }

        /* Password strength indicator */
        .password-hint {
            margin-top: 8px;
            padding: 12px;
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.4;
        }

        .password-hint.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .password-hint.danger {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .password-hint.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .password-requirements {
            margin-top: 5px;
            font-size: 13px;
        }

        .password-requirements ul {
            margin: 5px 0;
            padding-left: 20px;
        }

        .password-requirements li {
            margin: 2px 0;
        }

        .requirement-met {
            color: #28a745;
        }

        .requirement-unmet {
            color: #dc3545;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 32px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Loading spinner styles */
        .btn.loading {
            position: relative;
            color: transparent;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Icon Button Styles */
        .btn-icon {
            width: auto;
            min-width: 44px;
            height: 44px;
            padding: 8px 12px;
            margin-bottom: 0;
            margin-left: 8px;
            font-size: 18px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            position: relative;
        }

        .btn-icon:hover {
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-icon:first-child {
            margin-left: 0;
        }

        /* Tooltip styles for icon buttons */
        .btn-icon[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: normal;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
        }

        .btn-icon[title]:hover::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-bottom-color: rgba(0, 0, 0, 0.8);
            z-index: 1001;
            pointer-events: none;
        }

        /* Admin Styles */
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .btn-admin:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .btn-admin.btn-icon:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px) scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .admin-section {
            display: none;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .admin-panel {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .admin-panel h4 {
            margin-bottom: 15px;
            color: #495057;
            font-weight: 600;
        }

        .backup-controls, .user-controls {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .backup-controls .btn {
            flex: 1;
            min-width: 140px;
        }

        .status-display {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: white;
            border: 1px solid #e9ecef;
            min-height: 50px;
        }

        .users-list {
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            margin: 8px 0;
            border-radius: 6px;
            background: white;
            transition: all 0.2s ease;
        }

        .user-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .user-disabled {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            opacity: 0.8;
        }

        .user-info {
            flex: 1;
        }

        .user-email {
            font-weight: 600;
            color: #495057;
        }

        .user-meta {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .toggle-user-btn {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .toggle-user-btn.enable {
            background: #28a745;
            color: white;
        }

        .toggle-user-btn.disable {
            background: #dc3545;
            color: white;
        }

        .toggle-user-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-user-btn:disabled {
            background: #6c757d !important;
            color: #fff !important;
            cursor: not-allowed !important;
            opacity: 0.6;
            transform: none !important;
            box-shadow: none !important;
        }

        .toggle-user-btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Loading state for toggle-user-btn */
        .toggle-user-btn.loading {
            color: transparent !important;
            pointer-events: none;
        }

        .toggle-user-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .user-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .user-status.active {
            background: #d4edda;
            color: #155724;
        }

        .user-status.disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            padding: 15px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }

        /* Progress bar styles */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: rgba(102, 126, 234, 0.2);
            z-index: 9999;
            display: none;
        }

        .progress-bar.active {
            display: block;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            animation: progress 2s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .btn-secondary {
            background: #6c757d;
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-danger {
            background: #dc3545;
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            margin-right: 5px;
            margin-bottom: 0;
            width: auto;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
            width: auto;
            padding: 10px 20px;
            font-size: 14px;
            margin-bottom: 0;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .modal-header h3 {
            margin: 0;
            color: #495057;
            font-size: 18px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            padding: 25px;
            max-height: 50vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background: #f8f9fa;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }

        /* 2FA Styles */
        .twofa-status-container {
            margin-top: 15px;
        }

        .twofa-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-indicator.enabled {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-indicator.disabled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-indicator.checking {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .twofa-setup-steps {
            margin-bottom: 20px;
        }

        .step-item {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .step-item:last-child {
            border-bottom: none;
        }

        .step-item h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
        }

        .secret-manual {
            margin-top: 20px;
        }

        .secret-code {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 10px;
        }

        .secret-code input {
            flex: 1;
            font-family: monospace;
            font-size: 14px;
            background-color: #f8f9fa;
        }

        .backup-code-option {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .backup-files-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .backup-file-item {
            padding: 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .backup-file-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .backup-file-item.selected {
            border-color: #667eea;
            background: #e7f3ff;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .backup-file-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .backup-file-details {
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
        }

        .error {
            color: #dc3545;
            margin-top: 10px;
            font-size: 14px;
        }

        .main-app {
            display: none;
            width: 100%;
            min-height: 100vh;
        }

        .main-app.show {
            display: block;
        }

        /* Main application styles */
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            color: white;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .auth-section {
            margin-bottom: 20px;
        }

        .auth-form {
            display: block;
        }

        .auth-form.hidden {
            display: none;
        }

        .auth-toggle {
            text-align: center;
            margin-top: 15px;
        }

        .auth-toggle a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
        }

        .auth-toggle a:hover {
            text-decoration: underline;
        }

        .auth-form h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .status {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .notes-section {
            display: none;
        }

        .notes-section.active {
            display: block;
        }

        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .notes-header h3 {
            color: #333;
            font-size: 1.5rem;
        }

        .header-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 15px;
        }

        .settings-group {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .settings-group h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }

        .settings-description {
            margin-bottom: 20px;
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
        }

        .info-item label {
            font-weight: bold;
            color: #555;
        }

        .info-item span {
            color: #333;
        }

        .note-controls {
            display: flex;
            gap: 10px;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .note-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #667eea;
            position: relative;
        }

        .note-item.edit-mode {
            background: #fff3cd;
            border-left-color: #ffc107;
            padding: 12px 16px;
        }

        .note-item.edit-mode .note-actions {
            margin-bottom: 8px;
        }

        .edit-textarea {
            width: 100%;
            min-height: 150px;
            max-height: 300px;
            padding: 12px;
            border: 2px solid #ffc107;
            border-radius: 6px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            margin-top: 8px;
            margin-right: 0;
            box-sizing: border-box;
        }

        .edit-actions {
            display: flex;
            gap: 10px;
            margin-top: 8px;
            justify-content: flex-end;
            align-items: center;
        }

        .note-actions {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            gap: 5px;
        }

        .note-meta {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .note-content {
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
            margin-right: 80px;
        }

        .note-item.edit-mode .note-content {
            margin-right: 0;
        }

        .note-content.collapsed {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .note-expand-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
            padding: 4px 8px;
            text-decoration: underline;
            align-self: flex-start;
        }

        .note-expand-btn:hover {
            color: #5a6fd8;
        }

        .char-counter {
            position: absolute;
            bottom: 8px;
            right: 8px;
            font-size: 12px;
            color: #666;
            background: rgba(255, 255, 255, 0.9);
            padding: 2px 6px;
            border-radius: 3px;
            pointer-events: none;
        }

        .edit-char-counter {
            font-size: 12px;
            color: #666;
            margin-right: auto;
            padding: 2px 6px;
        }

        .edit-char-counter.warning {
            color: #ff6b35;
        }

        .edit-char-counter.error {
            color: #dc3545;
        }

        .char-counter.warning {
            color: #ff6b35;
        }

        .char-counter.error {
            color: #dc3545;
        }

        .form-group {
            position: relative;
        }

        .search-container {
            position: relative;
            display: inline-block;
            margin-left: 15px;
        }

        .search-container input {
            width: 200px;
            padding: 8px 30px 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .search-container input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-clear-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn:hover {
            color: #666;
        }

        .search-container input:not(:placeholder-shown) + .search-clear-btn {
            display: flex;
        }

        .search-section {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .search-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-scope-select {
            padding: 6px 8px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 12px;
            background: white;
            cursor: pointer;
            min-width: 90px;
        }

        .search-scope-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .crypto-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .crypto-info h3 {
            color: #0066cc;
            margin-bottom: 8px;
        }

        .crypto-info p {
            font-size: 14px;
            color: #0066cc;
            margin-bottom: 4px;
        }

        .edit-mode {
            background: #fff3cd;
            border-left-color: #ffc107;
        }

        .edit-textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
            margin-right: 0;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .auth-section {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2rem;
            }

            .note-content {
                margin-right: 0;
                margin-bottom: 40px;
            }

            .note-actions {
                position: static;
                margin-top: 10px;
                justify-content: flex-end;
            }

            .note-controls {
                flex-direction: column;
                gap: 10px;
            }

            .search-section {
                margin-left: 0;
                align-self: stretch;
                flex-direction: column;
                align-items: stretch;
            }

            .search-controls {
                flex-direction: column;
                gap: 8px;
            }

            .search-container {
                margin-left: 0;
            }

            .search-container input {
                width: 100%;
            }

            .search-scope-select {
                width: 100%;
                min-width: auto;
            }

            .notes-header {
                flex-direction: column;
                align-items: stretch;
            }

            .note-controls {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Progress bar for loading operations -->
    <div id="progress-bar" class="progress-bar"></div>

    <!-- Loading state -->
    <div id="loading-state" class="access-gate">
        <div class="lock-icon">⏳</div>
        <h1>Loading...</h1>
        <p class="subtitle">Checking access requirements...</p>
    </div>

    <!-- Access password verification layer -->
    <div id="access-gate" class="access-gate" style="display: none;">
        <div class="lock-icon">🔒</div>
        <h1>Access Verification</h1>
        <p class="subtitle">Please enter the site access password to continue</p>

        <div class="form-group">
            <label for="site-password">Access Password</label>
            <div class="password-container">
                <input type="password" id="site-password" placeholder="Enter access password" autocomplete="off">
                <span class="password-toggle" onclick="togglePasswordVisibility('site-password')">👁️</span>
            </div>
        </div>

        <button class="btn" id="verify-access-btn">Verify Access</button>
        <div id="access-error" class="error"></div>
    </div>

    <!-- Main application -->
    <div id="main-app" class="main-app">
        <div class="container">
            <div class="header">
                <h1>🔐 SecureNotes</h1>
                <p>End-to-end encrypted secure notes application</p>
            </div>

            <div class="crypto-info">
                <h3>🛡️ Security Guarantee</h3>
                <p>• All notes use end-to-end encryption, server cannot read content</p>
                <p>• Uses AES-256-GCM encryption algorithm and PBKDF2 key derivation</p>
                <p>• Master password never leaves your device</p>
                <p>• Access password protection prevents unauthorized access</p>
            </div>

            <!-- Authentication area -->
            <div id="auth-section" class="card">
                <div class="auth-section">
                    <div class="auth-form" id="login-form">
                        <h3>Login</h3>
                        <div class="form-group">
                            <label for="login-email">Email</label>
                            <input type="email" id="login-email" placeholder="<EMAIL>" maxlength="254">
                        </div>
                        <div class="form-group">
                            <label for="login-password">Master Password</label>
                            <div class="password-container">
                                <input type="password" id="login-password" placeholder="Enter master password" maxlength="64">
                                <span class="password-toggle" onclick="togglePasswordVisibility('login-password')">👁️</span>
                            </div>
                        </div>
                        <button class="btn" id="login-btn">Login</button>
                        <div class="auth-toggle">
                            <a id="show-register">Don't have an account? Register here</a>
                        </div>
                    </div>

                    <div class="auth-form hidden" id="register-form">
                        <h3>Register</h3>
                        <div class="form-group">
                            <label for="register-email">Email</label>
                            <input type="email" id="register-email" placeholder="<EMAIL>" maxlength="254">
                        </div>
                        <div class="form-group">
                            <label for="register-password">Master Password</label>
                            <div class="password-container">
                                <input type="password" id="register-password" placeholder="Create master password (8-64 characters)" minlength="8" maxlength="64">
                                <span class="password-toggle" onclick="togglePasswordVisibility('register-password')">👁️</span>
                            </div>
                            <div id="password-strength-hint" class="password-hint" style="display: none;">
                                <div><strong>💡 Password Security Recommendations:</strong></div>
                                <div class="password-requirements">
                                    <strong>For optimal security, use a strong password with 12+ characters including:</strong>
                                    <ul id="password-requirements-list">
                                        <li id="req-length" class="requirement-unmet">✗ At least 12 characters (current minimum: 8)</li>
                                        <li id="req-uppercase" class="requirement-unmet">✗ Uppercase letters (A-Z)</li>
                                        <li id="req-lowercase" class="requirement-unmet">✗ Lowercase letters (a-z)</li>
                                        <li id="req-numbers" class="requirement-unmet">✗ Numbers (0-9)</li>
                                        <li id="req-symbols" class="requirement-unmet">✗ Special characters (!@#$%^&*)</li>
                                    </ul>
                                    <div style="margin-top: 8px;">
                                        <strong>⚠️ Weak Password Risks:</strong><br>
                                        • Vulnerable to dictionary attacks and brute force<br>
                                        • May compromise your encrypted notes if breached<br>
                                        • Easier for attackers to guess or crack
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="btn" id="register-btn">Register</button>
                        <div class="auth-toggle">
                            <a id="show-login">Already have an account? Login here</a>
                        </div>
                    </div>
                </div>
                <div id="auth-status"></div>
            </div>

            <!-- Notes area -->
            <div id="notes-section" class="notes-section">
                <div class="card">
                    <div class="notes-header">
                        <div>
                            <h3>My Encrypted Notes</h3>
                            <div id="last-login-info" style="font-size: 0.9rem; color: #666; margin-top: 5px;"></div>
                        </div>
                        <div class="header-buttons">
                            <button class="btn btn-admin btn-icon" id="admin-btn" style="display: none;" title="Admin">🔧</button>
                            <button class="btn btn-admin btn-icon" id="settings-btn" title="Settings">⚙️</button>
                            <button class="btn btn-admin btn-icon" id="logout-btn" title="Logout">🚪</button>
                        </div>
                    </div>

                    <div id="notes-status"></div>

                    <div class="form-group">
                        <label for="note-content">Write Note</label>
                        <textarea id="note-content" placeholder="Write your thoughts here..." maxlength="1000"></textarea>
                        <div class="char-counter" id="note-char-counter">0/1000</div>
                    </div>

                    <div class="note-controls">
                        <button class="btn" id="save-note-btn">Save Note</button>
                        <button class="btn btn-danger" id="clear-all-notes-btn">Clear All Notes</button>
                        <!-- Search controls -->
                        <div class="search-section">
                            <div class="search-controls">
                                <select id="search-scope" class="search-scope-select" title="Search scope">
                                    <option value="title">Title only (Fast)</option>
                                    <option value="full">Full content (Slower)</option>
                                </select>
                                <div class="search-container">
                                    <input type="text" id="note-search" placeholder="Search notes..." />
                                    <button class="search-clear-btn" id="search-clear-btn" title="Clear search">×</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="notes-list" style="margin-top: 30px;"></div>

                    <!-- Pagination controls -->
                    <div id="pagination-controls" style="text-align: center; margin-top: 20px; display: none;">
                        <button class="btn btn-secondary" id="prev-page-btn" disabled>Previous</button>
                        <span id="page-info" style="margin: 0 15px;">Page 1 of 1</span>
                        <button class="btn btn-secondary" id="next-page-btn" disabled>Next</button>
                    </div>
                </div>
            </div>

            <!-- Admin section -->
            <div id="admin-section" class="admin-section">
                <div class="card">
                    <div class="admin-header">
                        <h3>🔧 System Administration</h3>
                        <button class="btn btn-secondary" id="back-to-notes-btn">← Back to Notes</button>
                    </div>

                    <!-- Backup Management -->
                    <div class="admin-panel">
                        <h4>📦 Backup Management</h4>
                        <div class="backup-controls">
                            <button class="btn btn-secondary" id="backup-status-btn">View Backup Status</button>
                            <button class="btn btn-primary" id="manual-backup-btn">Manual Backup</button>
                            <button class="btn btn-warning" id="restore-backup-btn">Restore Backup</button>
                        </div>
                        <div id="backup-status-display" class="status-display"></div>
                        <div id="restore-backup-modal" class="modal" style="display: none;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>🔄 Restore Database from Backup</h3>
                                    <span class="close" id="restore-modal-close">&times;</span>
                                </div>
                                <div class="modal-body">
                                    <div class="warning-box">
                                        <strong>⚠️ Warning:</strong> This operation will completely replace all current data with the backup data. This action cannot be undone!
                                    </div>
                                    <div id="backup-files-list" class="backup-files-container">
                                        <p>Loading backup files...</p>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-secondary" id="restore-cancel-btn">Cancel</button>
                                    <button class="btn btn-danger" id="restore-confirm-btn" disabled>Restore Selected Backup</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="admin-panel">
                        <h4>👥 User Management</h4>
                        <div class="user-controls">
                            <button class="btn btn-secondary" id="refresh-users-btn">Refresh User List</button>
                        </div>
                        <div id="users-list" class="users-list"></div>
                    </div>
                </div>
            </div>

            <!-- Settings area -->
            <div id="settings-section" class="settings-section" style="display: none;">
                <div class="card">
                    <div class="settings-header">
                        <h3>⚙️ Settings</h3>
                        <button class="btn btn-secondary" id="back-to-notes-from-settings-btn">← Back to Notes</button>
                    </div>

                    <div id="settings-status"></div>

                    <!-- Change Password Section -->
                    <div class="settings-group">
                        <h4>🔐 Change Master Password</h4>
                        <p class="settings-description">
                            Changing your master password will re-encrypt all your notes with the new password.
                            <strong>Make sure to remember your new password - it cannot be recovered!</strong>
                        </p>

                        <div class="form-group">
                            <label for="current-password">Current Master Password</label>
                            <div class="password-container">
                                <input type="password" id="current-password" placeholder="Enter current password">
                                <span class="password-toggle" onclick="togglePasswordVisibility('current-password')">👁️</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="new-password">New Master Password</label>
                            <div class="password-container">
                                <input type="password" id="new-password" placeholder="Enter new password (min 8 characters)">
                                <span class="password-toggle" onclick="togglePasswordVisibility('new-password')">👁️</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm-password">Confirm New Password</label>
                            <div class="password-container">
                                <input type="password" id="confirm-password" placeholder="Confirm new password">
                                <span class="password-toggle" onclick="togglePasswordVisibility('confirm-password')">👁️</span>
                            </div>
                        </div>

                        <button class="btn btn-warning" id="change-password-btn">Change Password</button>
                    </div>

                    <!-- Two-Factor Authentication Section -->
                    <div class="settings-group">
                        <h4>🔐 Two-Factor Authentication</h4>
                        <p class="settings-description">
                            Add an extra layer of security to your account with time-based one-time passwords (TOTP).
                            You can use apps like Google Authenticator, Authy, or any compatible TOTP app.
                        </p>

                        <div class="twofa-status-container">
                            <div class="info-item">
                                <label>Status:</label>
                                <span id="2fa-status" class="status-indicator">Checking...</span>
                            </div>
                            <div class="twofa-controls">
                                <button id="2fa-setup-btn" class="btn btn-primary" onclick="show2FASetupModal()" style="display: none;">Enable 2FA</button>
                                <button id="2fa-disable-btn" class="btn btn-warning" onclick="show2FADisableModal()" style="display: none;">Disable 2FA</button>
                                <button id="2fa-backup-btn" class="btn btn-secondary" onclick="show2FABackupModal()" style="display: none;">Backup Codes</button>
                            </div>
                        </div>
                    </div>

                    <!-- Account Information Section -->
                    <div class="settings-group">
                        <h4>👤 Account Information</h4>
                        <div class="info-item">
                            <label>Email:</label>
                            <span id="account-email">-</span>
                        </div>
                        <div class="info-item">
                            <label>Auto-logout timeout:</label>
                            <span>3 minutes of inactivity</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2FA Setup Modal -->
    <div id="2fa-setup-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔐 Enable Two-Factor Authentication</h3>
                <span class="close" id="2fa-setup-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="twofa-setup-steps">
                    <div class="step-item">
                        <h4>Step 1: Install an Authenticator App</h4>
                        <p>Download and install an authenticator app such as:</p>
                        <ul>
                            <li>Google Authenticator</li>
                            <li>Authy</li>
                            <li>Microsoft Authenticator</li>
                            <li>Any TOTP-compatible app</li>
                        </ul>
                    </div>

                    <div class="step-item">
                        <h4>Step 2: Add Account to Your Authenticator App</h4>
                        <div class="qr-container">
                            <div id="qr-code-display"></div>
                            <div class="secret-manual">
                                <p>Alternative: Enter this secret manually in your app:</p>
                                <div class="secret-code">
                                    <input type="text" id="totp-secret" readonly>
                                    <button type="button" onclick="copyToClipboard('totp-secret')">Copy</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="step-item">
                        <h4>Step 3: Enter Verification Code</h4>
                        <p>Enter the 6-digit code from your authenticator app:</p>
                        <div class="form-group">
                            <input type="text" id="2fa-verify-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}">
                        </div>
                    </div>
                </div>
                <div id="2fa-setup-status"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="2fa-setup-cancel">Cancel</button>
                <button class="btn btn-primary" id="2fa-setup-verify">Verify & Enable</button>
            </div>
        </div>
    </div>

    <!-- 2FA Login Verification Modal -->
    <div id="2fa-login-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔐 Two-Factor Authentication Required</h3>
            </div>
            <div class="modal-body">
                <p>Please enter your 6-digit verification code from your authenticator app:</p>
                <div class="form-group">
                    <input type="text" id="2fa-login-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}" autofocus>
                </div>
                <div class="backup-code-option">
                    <p>Lost your device? <a href="#" onclick="show2FABackupCodeInput()">Use a backup code instead</a></p>
                    <div id="backup-code-input" style="display: none;">
                        <div class="form-group">
                            <label for="2fa-backup-code-login">Backup Code:</label>
                            <input type="text" id="2fa-backup-code-login" placeholder="XXXXXXXX" maxlength="8">
                        </div>
                    </div>
                </div>
                <div id="2fa-login-status"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="2fa-login-cancel">Cancel</button>
                <button class="btn btn-primary" id="2fa-login-verify">Verify</button>
            </div>
        </div>
    </div>

    <!-- 2FA Disable Modal -->
    <div id="2fa-disable-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔓 Disable Two-Factor Authentication</h3>
                <span class="close" id="2fa-disable-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="warning-box">
                    <strong>⚠️ Warning:</strong> Disabling 2FA will reduce your account security.
                    You will only need your email and password to log in.
                </div>
                <div class="form-group">
                    <label for="2fa-disable-code">Enter current 6-digit code to confirm:</label>
                    <input type="text" id="2fa-disable-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}">
                </div>
                <div id="2fa-disable-status"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="2fa-disable-cancel">Cancel</button>
                <button class="btn btn-warning" id="2fa-disable-confirm" onclick="disable2FA()">Disable 2FA</button>
            </div>
        </div>
    </div>

    <!-- 2FA Backup Codes Modal -->
    <div id="2fa-backup-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔑 Backup Codes</h3>
                <span class="close" id="2fa-backup-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="warning-box">
                    <strong>⚠️ Important:</strong> Save these backup codes in a safe place.
                    Each code can only be used once and will allow you to log in if you lose access to your authenticator app.
                </div>
                <div class="form-group">
                    <label for="2fa-backup-verify-code">Enter current 6-digit code to generate new backup codes:</label>
                    <input type="text" id="2fa-backup-verify-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}">
                </div>
                <div id="backup-codes-display" style="display: none;">
                    <h4>Your Backup Codes:</h4>
                    <div class="backup-codes-list"></div>
                    <div class="backup-codes-actions">
                        <button class="btn btn-secondary" onclick="downloadBackupCodes()">Download</button>
                        <button class="btn btn-secondary" onclick="printBackupCodes()">Print</button>
                    </div>
                </div>
                <div id="2fa-backup-status"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="2fa-backup-cancel">Cancel</button>
                <button class="btn btn-primary" id="2fa-backup-generate" onclick="generate2FABackupCodes()">Generate Codes</button>
            </div>
        </div>
    </div>

    <script>
        // Loading state management
        const LoadingManager = {
            activeOperations: new Set(),

            // Show loading state for a button
            showButtonLoading(buttonId) {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.classList.add('loading');
                    button.disabled = true;
                    this.activeOperations.add(buttonId);
                    this.showProgressBar();
                }
            },

            // Hide loading state for a button
            hideButtonLoading(buttonId) {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.classList.remove('loading');

                    // Check if button should remain disabled (e.g., admin account disable button)
                    const shouldStayDisabled = button.hasAttribute('data-permanently-disabled') ||
                                             button.title === "Cannot disable your own account";

                    if (!shouldStayDisabled) {
                        button.disabled = false;
                    }

                    this.activeOperations.delete(buttonId);

                    if (this.activeOperations.size === 0) {
                        this.hideProgressBar();
                    }
                }
            },

            // Show progress bar
            showProgressBar() {
                const progressBar = document.getElementById('progress-bar');
                if (progressBar) {
                    progressBar.classList.add('active');
                }
            },

            // Hide progress bar
            hideProgressBar() {
                const progressBar = document.getElementById('progress-bar');
                if (progressBar) {
                    progressBar.classList.remove('active');
                }
            },

            // Show loading for any operation
            showLoading(operationId, buttonId = null) {
                this.activeOperations.add(operationId);
                if (buttonId) {
                    this.showButtonLoading(buttonId);
                } else {
                    this.showProgressBar();
                }
            },

            // Hide loading for any operation
            hideLoading(operationId, buttonId = null) {
                this.activeOperations.delete(operationId);
                if (buttonId) {
                    this.hideButtonLoading(buttonId);
                } else if (this.activeOperations.size === 0) {
                    this.hideProgressBar();
                }
            }
        };

        // Security: Configuration constants with validation
        const CONFIG = {
            API_BASE: 'https://secure-notes-api.v8x.workers.dev',
            ALLOWED_DOMAINS: ['secure-notes-api.v8x.workers.dev'],
            SITE_ACCESS_ENABLED: true, // Note: This will be dynamically determined by backend SITE_ACCESS_REQUIRED env var
            MAX_NOTE_LENGTH: 10000,
            SESSION_TIMEOUT:  60 * 60 * 1000, // 1 hours
            AUTO_LOGOUT_INACTIVE: 3 * 60 * 1000,  // 3 minutes inactive auto logout
            REQUEST_TIMEOUT: 30000, // 30 seconds
            MAX_RETRY_ATTEMPTS: 3,
            // Security: Add integrity check (SHA256 hash of domain string)
            EXPECTED_DOMAIN_HASH: '8b0e7c9c4b5f6a2d1e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6' // Placeholder - will be updated with actual hash
        };

        // Security: Verify configuration integrity
        async function verifyConfigIntegrity() {
            try {
                const domainStr = CONFIG.ALLOWED_DOMAINS.join(',');
                const hash = await sha256(domainStr);
                const expectedHash = CONFIG.EXPECTED_DOMAIN_HASH;

                // Simple integrity check (not foolproof but adds a layer)
                if (hash !== expectedHash) {
                    // Configuration mismatch detected, but don't block functionality
                    // In production, this could trigger additional security measures
                }
            } catch (error) {
                // Configuration integrity check failed silently
            }
        }

        // Security: Validate API endpoint
        function validateApiEndpoint(url) {
            try {
                const urlObj = new URL(url);
                return CONFIG.ALLOWED_DOMAINS.includes(urlObj.hostname);
            } catch {
                return false;
            }
        }

        // Global state
        let currentUser = null;
        let encryptionKey = null;
        let notes = [];
        let editingNoteId = null;
        let lastLoginAt = null;
        let currentPage = 1;
        let totalPages = 1;
        let allNotes = [];
        let decryptedNotes = new Map(); // Store decrypted content
        let autoLogoutTimer;
        let lastActivity = Date.now();
        let accessToken = null; // Security: Store access token in memory only
        let accessTokenExpiry = null; // Track token expiration

        // Security: Login attempt tracking
        let loginAttempts = new Map(); // email -> {count, lastAttempt, blocked}
        const MAX_LOGIN_ATTEMPTS = 5;
        const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

        // Security: Memory-only session management (enhanced security)
        let sessionData = {
            siteAccessVerified: false,
            accessTimestamp: null,
            accessExpiry: null,
            lastActivity: Date.now()
        };

        // Security: Auto-lock configuration
        const AUTO_LOCK_TIMEOUT = 3 * 60 * 1000; // 3 minutes
        let autoLockTimer = null;

        /**
         * Security fix: HTML escape function to prevent XSS attacks
         */
        function escapeHTML(str) {
            if (typeof str !== 'string') return '';
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        }

        /**
         * Security: Input validation functions
         */
        function validateInput(input, type, maxLength = 1000) {
            if (!input || typeof input !== 'string') return false;
            if (input.length > maxLength) return false;

            switch (type) {
                case 'email':
                    return /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(input);
                case 'password':
                    return input.length >= 8;
                case 'noteId':
                    return /^\d+$/.test(input);
                default:
                    return true;
            }
        }

        /**
         * Admin functionality
         */
        let isAdmin = false;

        async function checkAdminStatus() {
            try {

                const result = await apiManager.checkAdminStatus();

                if (result.success && result.isAdmin) {
                    isAdmin = true;
                    showAdminButton();
                } else {
                    isAdmin = false;
                }
            } catch (error) {
                console.error('Admin check failed:', error);
                console.error('Error details:', error.message);
                isAdmin = false;
            }
        }

        function showAdminButton() {
            const adminBtn = document.getElementById('admin-btn');
            if (adminBtn) {
                adminBtn.style.display = 'inline-block';
            }
        }

        function hideAdminButton() {
            const adminBtn = document.getElementById('admin-btn');
            if (adminBtn) {
                adminBtn.style.display = 'none';
            }
        }

        function showAdminSection() {
            // Hide all sections first
            document.getElementById('notes-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'none';
            document.getElementById('admin-section').style.display = 'block';
            loadUsers();
        }

        function hideAdminSection() {
            // Hide admin section and show notes
            document.getElementById('admin-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'none';
            document.getElementById('notes-section').style.display = 'block';
        }

        async function loadUsers() {
            // Show loading state
            LoadingManager.showButtonLoading('refresh-users-btn');

            try {
                showStatus('backup-status-display', 'Loading user list...', 'info');

                const result = await apiManager.getUsers();
                if (result.success) {
                    displayUsers(result.users);
                    showStatus('backup-status-display', `Successfully loaded ${result.users.length} users`, 'success');
                } else {
                    showStatus('backup-status-display', result.error || 'Failed to load user list', 'error');
                }
            } catch (error) {
                console.error('Load users error:', error);
                showStatus('backup-status-display', 'Failed to load user list', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('refresh-users-btn');
            }
        }

        function displayUsers(users) {
            const usersList = document.getElementById('users-list');
            if (!users || users.length === 0) {
                usersList.innerHTML = '<div class="status-display">No user data available</div>';
                return;
            }

            let html = '<div class="admin-stats">';
            const activeUsers = users.filter(u => !u.is_disabled).length;
            const disabledUsers = users.filter(u => u.is_disabled).length;
            const totalNotes = users.reduce((sum, u) => sum + (u.note_count || 0), 0);

            html += `
                <div class="stat-card">
                    <div class="stat-number">${users.length}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${activeUsers}</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${disabledUsers}</div>
                    <div class="stat-label">Disabled</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalNotes}</div>
                    <div class="stat-label">Total Notes</div>
                </div>
            </div>`;

            users.forEach(user => {
                const isDisabled = user.is_disabled === 1;
                const createdDate = new Date(user.created_at).toLocaleDateString();

                html += `
                    <div class="user-item ${isDisabled ? 'user-disabled' : ''}">
                        <div class="user-info">
                            <div class="user-email">${escapeHtml(user.email)}</div>
                            <div class="user-meta">
                                Registration: ${createdDate} | Notes: ${user.note_count || 0}
                            </div>
                        </div>
                        <div class="user-actions">
                            <span class="user-status ${isDisabled ? 'disabled' : 'active'}">
                                ${isDisabled ? 'Disabled' : 'Active'}
                            </span>
                            <button class="toggle-user-btn ${isDisabled ? 'enable' : 'disable'}"
                                    id="toggle-user-${user.id}"
                                    onclick="toggleUser(${user.id}, ${!isDisabled})"
                                    ${user.email === getCurrentAdminEmail() ? 'disabled title="Cannot disable your own account"' : ''}>
                                ${isDisabled ? 'Enable' : 'Disable'}
                            </button>
                        </div>
                    </div>
                `;
            });

            usersList.innerHTML = html;
        }

        function getCurrentAdminEmail() {
            // Get admin email from current login information
            // Simplified handling, should actually get from backend
            return currentUser || '';
        }

        async function toggleUser(userId, disable) {
            // Check if button is disabled (admin account protection)
            const buttonId = `toggle-user-${userId}`;
            const button = document.getElementById(buttonId);

            if (button && button.disabled && button.title === "Cannot disable your own account") {
                showStatus('backup-status-display', 'Cannot disable your own admin account', 'error');
                return;
            }

            if (disable && !confirm(`Are you sure you want to ${disable ? 'disable' : 'enable'} this user?`)) {
                return;
            }

            // Show loading state for the specific user button
            LoadingManager.showButtonLoading(buttonId);

            try {
                const result = await apiManager.toggleUserStatus(userId, disable);
                if (result.success) {
                    showStatus('backup-status-display', result.message, 'success');
                    loadUsers(); // Reload user list (this will hide the loading state)
                } else {
                    showStatus('backup-status-display', result.error || 'Operation failed', 'error');
                }
            } catch (error) {
                console.error('Toggle user error:', error);
                showStatus('backup-status-display', 'Operation failed', 'error');
            } finally {
                // Hide loading state (in case loadUsers() doesn't get called)
                LoadingManager.hideButtonLoading(buttonId);
            }
        }

        async function loadBackupStatus() {
            // Show loading state
            LoadingManager.showButtonLoading('backup-status-btn');

            try {
                showStatus('backup-status-display', 'Querying backup status...', 'info');

                const result = await apiManager.getBackupStatus();
                if (result.success) {
                    const provider = result.providerStatus || {};
                    let providerDetails = '';

                    // Generate provider-specific status details
                    if (provider.provider === 'Backblaze B2') {
                        providerDetails = `
                            <p><strong>Application Key ID:</strong> ${provider.hasApplicationKeyId ? 'Configured' : 'Not configured'}</p>
                            <p><strong>Application Key:</strong> ${provider.hasApplicationKey ? 'Configured' : 'Not configured'}</p>
                            <p><strong>Bucket ID:</strong> ${provider.hasBucketId ? 'Configured' : 'Not configured'}</p>
                        `;
                    } else if (provider.provider === 'Google Drive') {
                        providerDetails = `
                            <p><strong>Folder ID:</strong> ${provider.hasFolderId ? 'Configured' : 'Not configured'}</p>
                            <p><strong>Credentials:</strong> ${provider.hasCredentials ? 'Configured' : 'Not configured'}</p>
                        `;
                    } else if (provider.provider === 'OneDrive') {
                        providerDetails = `
                            <p><strong>Drive ID:</strong> ${provider.hasDriveId ? 'Configured' : 'Not configured'}</p>
                            <p><strong>Credentials:</strong> ${provider.hasCredentials ? 'Configured' : 'Not configured'}</p>
                        `;
                    }

                    const statusHtml = `
                        <h5>Backup Configuration Status</h5>
                        <p><strong>Backup Provider:</strong> ${result.backupProvider || 'Not specified'}</p>
                        <p><strong>Provider:</strong> ${provider.provider || 'Unknown'}</p>
                        <p><strong>Backup Enabled:</strong> ${result.backupEnabled ? 'Yes' : 'No'}</p>
                        <p><strong>Retention Count:</strong> ${result.retentionCount} backups</p>
                        ` + providerDetails + `
                        <p><strong>Last Backup Location:</strong> ${provider.lastBackupLocation || 'Check your backup provider'}</p>
                    `;
                    document.getElementById('backup-status-display').innerHTML = statusHtml;
                } else {
                    showStatus('backup-status-display', result.error || 'Failed to query backup status', 'error');
                }
            } catch (error) {
                console.error('Load backup status error:', error);
                showStatus('backup-status-display', 'Failed to query backup status', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('backup-status-btn');
            }
        }

        async function triggerManualBackup() {
            if (!confirm('Are you sure you want to perform a manual backup? This operation may take some time.')) {
                return;
            }

            // Show loading state
            LoadingManager.showButtonLoading('manual-backup-btn');

            try {
                showStatus('backup-status-display', 'Performing backup...', 'info');

                const result = await apiManager.triggerBackup();
                if (result.success) {
                    showStatus('backup-status-display', 'Backup completed successfully!', 'success');
                } else {
                    showStatus('backup-status-display', result.error || 'Backup failed', 'error');
                }
            } catch (error) {
                console.error('Manual backup error:', error);
                showStatus('backup-status-display', 'Backup failed', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('manual-backup-btn');
            }
        }

        let selectedBackupFileId = null;

        async function showRestoreBackupModal() {
            const modal = document.getElementById('restore-backup-modal');
            const filesList = document.getElementById('backup-files-list');
            const confirmBtn = document.getElementById('restore-confirm-btn');

            // Reset state
            selectedBackupFileId = null;
            confirmBtn.disabled = true;

            // Show modal
            modal.style.display = 'flex';

            // Load backup files
            filesList.innerHTML = '<p>Loading backup files...</p>';

            try {
                const result = await apiManager.getBackupFilesList();
                if (result.success && result.files && result.files.length > 0) {
                    displayBackupFiles(result.files, result.provider);
                } else {
                    filesList.innerHTML = '<p>No backup files found.</p>';
                }
            } catch (error) {
                console.error('Failed to load backup files:', error);
                filesList.innerHTML = '<p class="error">Failed to load backup files. Please try again.</p>';
            }
        }

        function displayBackupFiles(files, provider) {
            const filesList = document.getElementById('backup-files-list');

            let html = `<p><strong>Backup Provider:</strong> ${provider}</p>`;
            html += '<div class="backup-files-grid">';

            files.forEach(file => {
                const date = new Date(file.uploadTimestamp);
                const formattedDate = date.toLocaleString();
                const fileSize = formatFileSize(file.size);

                html += `
                    <div class="backup-file-item" data-file-id="${escapeHtml(file.fileId)}" onclick="selectBackupFile('${escapeHtml(file.fileId)}', this)">
                        <div class="backup-file-name">${escapeHtml(file.fileName)}</div>
                        <div class="backup-file-details">
                            <span>📅 ${formattedDate}</span>
                            <span>📦 ${fileSize}</span>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            filesList.innerHTML = html;
        }

        function selectBackupFile(fileId, element) {
            // Remove previous selection
            document.querySelectorAll('.backup-file-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Select current item
            element.classList.add('selected');
            selectedBackupFileId = fileId;

            // Enable confirm button
            document.getElementById('restore-confirm-btn').disabled = false;
        }

        function hideRestoreBackupModal() {
            document.getElementById('restore-backup-modal').style.display = 'none';
            selectedBackupFileId = null;
        }

        async function confirmRestoreBackup() {
            if (!selectedBackupFileId) {
                alert('Please select a backup file to restore.');
                return;
            }

            const finalConfirm = confirm(
                '⚠️ FINAL WARNING ⚠️\n\n' +
                'This will PERMANENTLY DELETE all current data and replace it with the selected backup.\n\n' +
                'This action CANNOT be undone!\n\n' +
                'Are you absolutely sure you want to proceed?'
            );

            if (!finalConfirm) {
                return;
            }

            // Show loading state
            const confirmBtn = document.getElementById('restore-confirm-btn');
            const originalText = confirmBtn.textContent;
            confirmBtn.disabled = true;
            confirmBtn.textContent = 'Restoring...';

            try {
                const result = await apiManager.restoreFromBackup(selectedBackupFileId, 'RESTORE_DATABASE_FROM_BACKUP');

                if (result.success) {
                    alert('Database restored successfully!\n\nYou will be logged out and need to log in again.');
                    hideRestoreBackupModal();
                    logout(); // Force logout to refresh session
                } else {
                    alert('Restore failed: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Restore error:', error);
                alert('Restore failed: ' + error.message);
            } finally {
                confirmBtn.disabled = false;
                confirmBtn.textContent = originalText;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * HTML escape utility function
         */
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * Password visibility toggle function
         */
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const toggle = input.parentElement.querySelector('.password-toggle');

            if (input.type === 'password') {
                input.type = 'text';
                toggle.textContent = '🙈'; // Hide icon
            } else {
                input.type = 'password';
                toggle.textContent = '👁️'; // Show icon
            }
        }

        /**
         * Password strength analysis
         */
        function analyzePasswordStrength(password) {
            const requirements = {
                length: password.length >= 12,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                numbers: /\d/.test(password),
                symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            const score = Object.values(requirements).filter(Boolean).length;
            const hasMinLength = password.length >= 8;

            return {
                requirements,
                score,
                hasMinLength,
                isStrong: score >= 4 && requirements.length,
                isWeak: score <= 2 || password.length < 8
            };
        }

        /**
         * Update password strength indicator
         */
        function updatePasswordStrengthIndicator(password) {
            const hintElement = document.getElementById('password-strength-hint');
            const requirementsList = document.getElementById('password-requirements-list');

            if (!password || password.length === 0) {
                hintElement.style.display = 'none';
                return;
            }

            hintElement.style.display = 'block';
            const analysis = analyzePasswordStrength(password);

            // Update requirement indicators
            const reqElements = {
                length: document.getElementById('req-length'),
                uppercase: document.getElementById('req-uppercase'),
                lowercase: document.getElementById('req-lowercase'),
                numbers: document.getElementById('req-numbers'),
                symbols: document.getElementById('req-symbols')
            };

            // Update each requirement
            reqElements.length.textContent = analysis.requirements.length ? '✓ At least 12 characters' : '✗ At least 12 characters (current minimum: 8)';
            reqElements.length.className = analysis.requirements.length ? 'requirement-met' : 'requirement-unmet';

            reqElements.uppercase.textContent = analysis.requirements.uppercase ? '✓ Uppercase letters (A-Z)' : '✗ Uppercase letters (A-Z)';
            reqElements.uppercase.className = analysis.requirements.uppercase ? 'requirement-met' : 'requirement-unmet';

            reqElements.lowercase.textContent = analysis.requirements.lowercase ? '✓ Lowercase letters (a-z)' : '✗ Lowercase letters (a-z)';
            reqElements.lowercase.className = analysis.requirements.lowercase ? 'requirement-met' : 'requirement-unmet';

            reqElements.numbers.textContent = analysis.requirements.numbers ? '✓ Numbers (0-9)' : '✗ Numbers (0-9)';
            reqElements.numbers.className = analysis.requirements.numbers ? 'requirement-met' : 'requirement-unmet';

            reqElements.symbols.textContent = analysis.requirements.symbols ? '✓ Special characters (!@#$%^&*)' : '✗ Special characters (!@#$%^&*)';
            reqElements.symbols.className = analysis.requirements.symbols ? 'requirement-met' : 'requirement-unmet';

            // Update hint style based on strength
            hintElement.className = 'password-hint';
            if (analysis.isStrong) {
                hintElement.classList.add('success');
            } else if (analysis.isWeak) {
                hintElement.classList.add('danger');
            } else {
                hintElement.classList.add('warning');
            }
        }

        /**
         * Security: Check if account is temporarily locked due to failed attempts
         */
        function isAccountLocked(email) {
            const attempts = loginAttempts.get(email);
            if (!attempts) return false;

            const now = Date.now();
            if (attempts.blocked && (now - attempts.lastAttempt) < LOCKOUT_DURATION) {
                return true;
            }

            // Reset if lockout period has passed
            if (attempts.blocked && (now - attempts.lastAttempt) >= LOCKOUT_DURATION) {
                loginAttempts.delete(email);
                return false;
            }

            return false;
        }

        /**
         * Security: Record failed login attempt
         */
        function recordFailedLogin(email) {
            const now = Date.now();
            const attempts = loginAttempts.get(email) || {count: 0, lastAttempt: 0, blocked: false};

            attempts.count++;
            attempts.lastAttempt = now;

            if (attempts.count >= MAX_LOGIN_ATTEMPTS) {
                attempts.blocked = true;
                showStatus('auth-status', `Account temporarily locked due to too many failed attempts. Try again in 15 minutes.`, 'error');
            }

            loginAttempts.set(email, attempts);
        }

        /**
         * Security: Clear login attempts on successful login
         */
        function clearLoginAttempts(email) {
            loginAttempts.delete(email);
        }

        /**
         * Memory-only storage for enhanced security
         * No persistent storage to prevent data leakage
         */
        class MemoryOnlyStorage {
            constructor() {
                this.memoryStorage = new Map();
            }

            setItem(key, value) {
                this.memoryStorage.set(key, value);
            }

            getItem(key) {
                return this.memoryStorage.get(key) || null;
            }

            removeItem(key) {
                this.memoryStorage.delete(key);
            }

            clear() {
                this.memoryStorage.clear();
            }
        }

        // Initialize memory-only storage
        const storage = new MemoryOnlyStorage();

        /**
         * Security: Secure hash function
         */
        async function sha256(message) {
            const msgBuffer = new TextEncoder().encode(message);
            const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        /**
         * Security: Secure API request function with validation
         */
        async function secureApiRequest(endpoint, options = {}) {
            const url = `${CONFIG.API_BASE}${endpoint}`;

            // Validate API endpoint
            if (!validateApiEndpoint(url)) {
                throw new Error('Invalid API endpoint');
            }

            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            // Special cases: These endpoints don't need access token
            if (endpoint === '/api/verify-access' || endpoint === '/api/check-access-requirement') {
                // No access token required for these endpoints
            } else {
                // For all other endpoints, check access token
                if (accessToken && accessTokenExpiry && Date.now() < accessTokenExpiry) {
                    headers['X-Access-Token'] = accessToken;
                } else if (accessToken && accessTokenExpiry && Date.now() >= accessTokenExpiry) {
                    // Access token expired, clear it and re-check access requirements
                    accessToken = null;
                    accessTokenExpiry = null;
                    sessionData.siteAccessVerified = false;
                    sessionData.accessTimestamp = null;
                    sessionData.accessExpiry = null;

                    // Hide main app and show loading state
                    document.getElementById('main-app').classList.remove('show');
                    document.getElementById('access-gate').style.display = 'none';
                    document.getElementById('loading-state').style.display = 'block';

                    // Re-check access requirements
                    setTimeout(() => {
                        checkAccessRequirement();
                    }, 100);

                    throw new Error('Access session expired. Re-checking access requirements...');
                } else if (!accessToken) {
                    // No access token available
                    throw new Error('Site access verification required. Please refresh the page and verify access.');
                }
            }

            const requestOptions = {
                ...options,
                headers,
                signal: AbortSignal.timeout(CONFIG.REQUEST_TIMEOUT)
            };

            const response = await fetch(url, requestOptions);

            if (!response.ok) {
                const error = await response.json().catch(() => ({ error: 'Request failed' }));
                throw new Error(error.error || 'Request failed');
            }

            return response.json();
        }

        /**
         * Security: Enhanced site access verification with server-side validation
         */
        async function verifySiteAccess() {
            const password = document.getElementById('site-password').value;
            const errorEl = document.getElementById('access-error');

            if (!validateInput(password, 'password')) {
                errorEl.textContent = 'Please enter a valid access password';
                return;
            }

            // Show loading state
            LoadingManager.showButtonLoading('verify-access-btn');
            errorEl.textContent = '';

            try {
                // Calculate hash of input password
                const hashedInput = await sha256(password);

                // Get access hash from server for verification
                const result = await secureApiRequest('/api/verify-access', {
                    method: 'POST',
                    body: JSON.stringify({ hash: hashedInput })
                });

                if (result.success && result.accessToken) {
                    // Security: Store access token in memory (privacy mode compatible)
                    accessToken = result.accessToken;
                    accessTokenExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

                    // Clear password input for security
                    document.getElementById('site-password').value = '';

                    document.getElementById('access-gate').style.display = 'none';
                    document.getElementById('main-app').classList.add('show');

                    // Security: Update session data in memory
                    sessionData.siteAccessVerified = true;
                    sessionData.accessTimestamp = Date.now();
                    sessionData.accessExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

                    // Start auto logout timer
                    startAutoLogoutTimer();
                } else {
                    errorEl.textContent = 'Invalid access password, please try again';
                }
            } catch (error) {
                console.error('Verification failed:', error);
                errorEl.textContent = 'Verification failed, please check network connection';
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('verify-access-btn');
            }
        }

        /**
         * Security: Enhanced auto logout functionality
         */
        function startAutoLogoutTimer() {
            function checkInactivity() {
                if (Date.now() - lastActivity > CONFIG.AUTO_LOGOUT_INACTIVE) {
                    logout();
                    alert('You have been automatically logged out due to inactivity');
                }
            }

            inactivityTimer = setInterval(checkInactivity, 60000); // Check every minute

            // Listen for user activity
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => {
                    lastActivity = Date.now();
                }, { passive: true });
            });
        }

        /**
         * Security: Check if site access password is required
         */
        async function checkAccessRequirement() {
            // Show loading state
            LoadingManager.showLoading('check-access');

            try {
                const response = await secureApiRequest('/api/check-access-requirement');

                // Hide loading state
                document.getElementById('loading-state').style.display = 'none';

                if (response.success) {
                    if (!response.siteAccessRequired) {
                        // Site access not required, use auto-generated token
                        accessToken = response.accessToken;
                        accessTokenExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

                        // Update session data
                        sessionData.siteAccessVerified = true;
                        sessionData.accessTimestamp = Date.now();
                        sessionData.accessExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours

                        // Check if user is already logged in
                        if (currentUser && apiManager && apiManager.isTokenValid()) {
                            // User is logged in, show notes page directly
                            document.getElementById('main-app').classList.add('show');
                            showNotesSection();
                            // Check admin permissions
                            checkAdminStatus();
                        } else {
                            // No user logged in, show login page
                            document.getElementById('main-app').classList.add('show');
                            showAuthSection();
                        }
                        return;
                    } else {
                        // Site access required, show access gate
                        document.getElementById('access-gate').style.display = 'block';
                        // Check existing session
                        checkSiteAccess();
                    }
                } else {
                    // Fallback to requiring access password
                    document.getElementById('access-gate').style.display = 'block';
                    checkSiteAccess();
                }
            } catch (error) {
                console.error('Error checking access requirement:', error);
                // Hide loading state and fallback to requiring access password
                document.getElementById('loading-state').style.display = 'none';
                document.getElementById('access-gate').style.display = 'block';
                checkSiteAccess();
            } finally {
                // Hide loading state
                LoadingManager.hideLoading('check-access');
            }
        }

        /**
         * Security: Memory-only access verification check (enhanced security)
         */
        function checkSiteAccess() {
            try {
                // Check memory-based session data only (no persistent storage)
                if (sessionData.siteAccessVerified &&
                    sessionData.accessExpiry &&
                    Date.now() < sessionData.accessExpiry &&
                    accessToken &&
                    accessTokenExpiry &&
                    Date.now() < accessTokenExpiry) {

                    document.getElementById('access-gate').style.display = 'none';
                    document.getElementById('main-app').classList.add('show');

                    // Check if user is already logged in and show appropriate page
                    if (currentUser && apiManager && apiManager.isTokenValid()) {
                        // User is logged in, show notes page directly
                        showNotesSection();
                        // Check admin permissions
                        checkAdminStatus();
                    } else {
                        // No user logged in, show login page
                        showAuthSection();
                    }

                    // Start auto-logout timer
                    startAutoLogoutTimer();
                    return;
                }

                // No valid session found - clear session data
                // Note: Access gate display is now controlled by checkAccessRequirement()
                clearSessionData();
            } catch (error) {
                console.error('Error checking site access:', error);
                clearSessionData();
            }
        }

        /**
         * Clear all session data (memory-only for enhanced security)
         */
        function clearSessionData() {
            sessionData.siteAccessVerified = false;
            sessionData.accessTimestamp = null;
            sessionData.accessExpiry = null;
            sessionData.lastActivity = Date.now();
            accessToken = null;
            accessTokenExpiry = null;

            // Clear auto-logout timer
            if (autoLogoutTimer) {
                clearTimeout(autoLogoutTimer);
                autoLogoutTimer = null;
            }

            // Clear memory storage
            storage.clear();
        }

        /**
         * Security: Auto-logout functionality
         */
        function startAutoLogoutTimer() {
            // Clear existing timer
            if (autoLogoutTimer) {
                clearTimeout(autoLogoutTimer);
            }

            autoLogoutTimer = setTimeout(() => {
                autoLogout();
            }, CONFIG.AUTO_LOGOUT_INACTIVE);
        }

        function resetAutoLogoutTimer() {
            sessionData.lastActivity = Date.now();
            if (currentUser) {
                startAutoLogoutTimer();
            }
        }

        function autoLogout() {
            console.info('Auto-logout due to inactivity');
            showStatus('auth-status', 'Automatically logged out due to inactivity', 'info');
            logout();
        }



        // Encryption utility functions
        class CryptoUtils {
            static async deriveKey(password, salt) {
                const enc = new TextEncoder();
                const keyMaterial = await window.crypto.subtle.importKey(
                    'raw',
                    enc.encode(password),
                    { name: 'PBKDF2' },
                    false,
                    ['deriveKey']
                );

                const key = await window.crypto.subtle.deriveKey(
                    {
                        name: 'PBKDF2',
                        salt: salt,
                        iterations: 200000, // Increased for better security
                        hash: 'SHA-256',
                    },
                    keyMaterial,
                    { name: 'AES-GCM', length: 256 },
                    true,
                    ['encrypt', 'decrypt']
                );

                return key;
            }

            static async deriveAuthKey(password, salt) {
                const enc = new TextEncoder();
                const keyMaterial = await window.crypto.subtle.importKey(
                    'raw',
                    enc.encode(password),
                    { name: 'PBKDF2' },
                    false,
                    ['deriveKey']
                );

                const authKey = await window.crypto.subtle.deriveKey(
                    {
                        name: 'PBKDF2',
                        salt: salt,
                        iterations: 300000, // Higher iterations for auth key security
                        hash: 'SHA-256',
                    },
                    keyMaterial,
                    { name: 'HMAC', hash: 'SHA-256' },
                    true,
                    ['sign']
                );

                const exported = await window.crypto.subtle.exportKey('raw', authKey);
                return btoa(String.fromCharCode(...new Uint8Array(exported)));
            }

            static async encryptText(plainText, key) {
                try {
                    // Validate inputs
                    if (!plainText || typeof plainText !== 'string') {
                        throw new Error('Invalid plaintext data');
                    }
                    if (!key) {
                        throw new Error('Invalid encryption key');
                    }

                    // Validate key before use
                    if (!(await this.validateKeyStrength(key))) {
                        throw new Error('Encryption key failed validation');
                    }

                    const enc = new TextEncoder();
                    const encodedText = enc.encode(plainText);

                    // Generate cryptographically secure random IV
                    const iv = window.crypto.getRandomValues(new Uint8Array(12));

                    const encrypted = await window.crypto.subtle.encrypt(
                        { name: 'AES-GCM', iv: iv },
                        key,
                        encodedText
                    );

                    const combined = new Uint8Array(iv.length + encrypted.byteLength);
                    combined.set(iv);
                    combined.set(new Uint8Array(encrypted), iv.length);

                    const result = btoa(String.fromCharCode(...combined));

                    // Clear sensitive data
                    this.clearSensitiveData(encodedText);
                    this.clearSensitiveData(iv);

                    return result;
                } catch (error) {
                    throw new Error('Encryption failed');
                }
            }

            static async decryptText(encryptedData, key) {
                try {
                    // Validate inputs
                    if (!encryptedData || typeof encryptedData !== 'string') {
                        throw new Error('Invalid encrypted data');
                    }
                    if (!key) {
                        throw new Error('Invalid decryption key');
                    }

                    const combined = new Uint8Array([...atob(encryptedData)].map(c => c.charCodeAt(0)));

                    // Validate minimum length (12 bytes IV + at least 1 byte data + 16 bytes auth tag)
                    if (combined.length < 29) {
                        throw new Error('Invalid encrypted data format');
                    }

                    const iv = combined.slice(0, 12);
                    const encrypted = combined.slice(12);

                    const decrypted = await window.crypto.subtle.decrypt(
                        { name: 'AES-GCM', iv: iv },
                        key,
                        encrypted
                    );

                    const dec = new TextDecoder();
                    const result = dec.decode(decrypted);

                    // Validate decrypted content is valid UTF-8
                    if (result.includes('\uFFFD')) {
                        throw new Error('Decrypted content contains invalid characters');
                    }

                    return result;
                } catch (error) {
                    // Security: Don't log specific error details that could leak information
                    throw new Error('Decryption failed');
                }
            }

            static generateSalt() {
                return window.crypto.getRandomValues(new Uint8Array(16));
            }

            // Security: Derive different salts for different purposes to avoid cryptographic conflicts
            static async deriveSubSalt(baseSalt, purpose) {
                const enc = new TextEncoder();
                const purposeBytes = enc.encode(purpose);
                const combined = new Uint8Array(baseSalt.length + purposeBytes.length);
                combined.set(baseSalt);
                combined.set(purposeBytes, baseSalt.length);

                const hashBuffer = await crypto.subtle.digest('SHA-256', combined);
                return new Uint8Array(hashBuffer.slice(0, 16)); // Use first 16 bytes as salt
            }

            // Security: Validate salt consistency
            static validateSaltConsistency(salt1, salt2) {
                if (!salt1 || !salt2) return false;
                if (salt1.length !== salt2.length) return false;
                return salt1.every((val, index) => val === salt2[index]);
            }

            // Security: Clear sensitive data from memory
            static clearSensitiveData(data) {
                if (data instanceof Uint8Array) {
                    data.fill(0);
                } else if (typeof data === 'string') {
                    // Note: In JavaScript, strings are immutable, but we can at least null the reference
                    data = null;
                }
            }

            // Security: Validate encryption key strength
            static async validateKeyStrength(key) {
                try {
                    // Export key to check its properties
                    const exported = await window.crypto.subtle.exportKey('raw', key);
                    const keyBytes = new Uint8Array(exported);

                    // Check key length (should be 32 bytes for AES-256)
                    if (keyBytes.length !== 32) {
                        throw new Error('Invalid key length');
                    }

                    // Basic entropy check (not cryptographically rigorous but better than nothing)
                    const uniqueBytes = new Set(keyBytes).size;
                    if (uniqueBytes < 16) { // At least 16 different byte values
                        throw new Error('Key appears to have low entropy');
                    }

                    return true;
                } catch (error) {
                    console.error('Key validation failed:', error);
                    return false;
                }
            }
        }

        // Security: Enhanced API Manager with validation
        class APIManager {
            constructor() {
                this.apiBase = CONFIG.API_BASE;
                this.token = this.getSecureToken();
            }

            // Security: Memory-only token storage (enhanced security)
            getSecureToken() {
                // Always return null - no persistent token storage for enhanced security
                return null;
            }

            setSecureToken(token) {
                // Store only in memory for current session
                this.token = token;
                this.tokenExpiry = Date.now() + CONFIG.SESSION_TIMEOUT;
            }

            clearToken() {
                // Clear memory-only token
                this.token = null;
                this.tokenExpiry = null;
            }

            isTokenValid() {
                return this.token && this.tokenExpiry && Date.now() < this.tokenExpiry;
            }

            async request(endpoint, options = {}) {
                return await secureApiRequest(endpoint, {
                    ...options,
                    headers: {
                        ...options.headers,
                        ...(this.token && { 'Authorization': `Bearer ${this.token}` })
                    }
                });
            }

            async getSalt(email) {
                if (!validateInput(email, 'email')) {
                    throw new Error('Invalid email format');
                }

                const result = await this.request(`/api/salt?email=${encodeURIComponent(email)}`);
                if (result && result.salt) {
                    return {
                        success: true,
                        salt: result.salt
                    };
                }
                throw new Error('Unable to get user salt, user may not exist.');
            }

            async register(email, authKey, salt) {
                if (!validateInput(email, 'email')) {
                    throw new Error('Invalid email format');
                }
                if (!authKey || typeof authKey !== 'string') {
                    throw new Error('Invalid auth key');
                }

                return await this.request('/api/register', {
                    method: 'POST',
                    body: JSON.stringify({
                        email,
                        authKey,
                        salt: Array.from(salt)
                    })
                });
            }

            async login(email, authKey) {
                if (!validateInput(email, 'email')) {
                    throw new Error('Invalid email format');
                }
                if (!authKey || typeof authKey !== 'string') {
                    throw new Error('Invalid auth key');
                }

                const result = await this.request('/api/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        email,
                        authKey
                    })
                });

                if (result.success) {
                    this.setSecureToken(result.token);
                }

                return result;
            }

            async saveNote(encryptedContent, encryptedPreviewTitle) {
                if (!encryptedContent || typeof encryptedContent !== 'string') {
                    throw new Error('Invalid encrypted content');
                }

                return await this.request('/api/notes', {
                    method: 'POST',
                    body: JSON.stringify({
                        encryptedContent,
                        encryptedPreviewTitle
                    })
                });
            }

            // Admin API methods
            async checkAdminStatus() {
                return await this.request('/api/admin/check', {
                    method: 'GET'
                });
            }

            async getUsers() {
                return await this.request('/api/admin/users', {
                    method: 'GET'
                });
            }

            async toggleUserStatus(userId, disabled) {
                return await this.request(`/api/admin/users/${userId}/toggle`, {
                    method: 'PUT',
                    body: JSON.stringify({ disabled })
                });
            }

            async getBackupStatus() {
                return await this.request('/api/admin/backup/status', {
                    method: 'GET'
                });
            }

            async triggerBackup() {
                return await this.request('/api/admin/backup', {
                    method: 'POST'
                });
            }

            async getBackupFilesList() {
                return await this.request('/api/admin/backup/list', {
                    method: 'GET'
                });
            }

            async downloadBackupFile(fileId) {
                return await this.request(`/api/admin/backup/download/${fileId}`, {
                    method: 'GET'
                });
            }

            async restoreFromBackup(fileId, confirmation) {
                return await this.request('/api/admin/backup/restore', {
                    method: 'POST',
                    body: JSON.stringify({ fileId, confirmation })
                });
            }

            async updateNote(noteId, encryptedContent, encryptedPreviewTitle) {
                if (!validateInput(noteId, 'noteId')) {
                    throw new Error('Invalid note ID');
                }
                if (!encryptedContent || typeof encryptedContent !== 'string') {
                    throw new Error('Invalid encrypted content');
                }

                return await this.request(`/api/notes/${noteId}`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        encryptedContent,
                        encryptedPreviewTitle
                    })
                });
            }

            async deleteNote(noteId) {
                if (!validateInput(noteId, 'noteId')) {
                    throw new Error('Invalid note ID');
                }

                return await this.request(`/api/notes/${noteId}`, {
                    method: 'DELETE'
                });
            }

            async getNotes() {
                const result = await this.request('/api/notes');
                return result.notes || [];
            }

            async clearAllNotes() {
                return await this.request('/api/notes/clear', {
                    method: 'DELETE',
                    body: JSON.stringify({
                        confirmation: 'DELETE_ALL_NOTES'  // Required confirmation for security
                    })
                });
            }

            async changePassword(currentAuthKey, newAuthKey, reencryptedNotes) {
                if (!currentAuthKey || !newAuthKey || !Array.isArray(reencryptedNotes)) {
                    throw new Error('Invalid parameters for password change');
                }

                return await this.request('/api/change-password', {
                    method: 'POST',
                    body: JSON.stringify({
                        currentAuthKey,
                        newAuthKey,
                        reencryptedNotes
                    })
                });
            }

            // 2FA API methods
            async setup2FA() {
                return await this.request('/api/2fa/setup', {
                    method: 'POST'
                });
            }

            async verify2FA(token) {
                return await this.request('/api/2fa/verify', {
                    method: 'POST',
                    body: JSON.stringify({ token })
                });
            }

            async disable2FA(token) {
                return await this.request('/api/2fa/disable', {
                    method: 'POST',
                    body: JSON.stringify({ token })
                });
            }

            async generate2FABackupCodes(token) {
                return await this.request('/api/2fa/backup', {
                    method: 'POST',
                    body: JSON.stringify({ token })
                });
            }

            async get2FAStatus() {
                return await this.request('/api/2fa/status', {
                    method: 'GET'
                });
            }

            async verify2FALogin(tempToken, totpCode, backupCode) {
                return await this.request('/api/login/2fa', {
                    method: 'POST',
                    body: JSON.stringify({
                        tempToken,
                        totpCode,
                        backupCode
                    })
                });
            }

            logout() {
                this.clearToken();
            }
        }

        const apiManager = new APIManager();

        // Main application functions
        async function register() {
            const email = document.getElementById('register-email').value.trim();
            const password = document.getElementById('register-password').value;

            if (!validateInput(email, 'email') || !validateInput(password, 'password')) {
                showStatus('auth-status', 'Please fill in all fields correctly', 'error');
                return;
            }

            if (password.length < 8) {
                showStatus('auth-status', 'Master password must be at least 8 characters', 'error');
                return;
            }

            // Show loading state
            LoadingManager.showButtonLoading('register-btn');
            showStatus('auth-status', 'Creating account...', 'info');

            try {
                const baseSalt = CryptoUtils.generateSalt();
                const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
                const authKey = await CryptoUtils.deriveAuthKey(password, authSalt);
                const result = await apiManager.register(email, authKey, baseSalt);

                if (result.success) {
                    showStatus('auth-status', 'Registration successful! Switching to login...', 'success');
                    // Clear form for security
                    document.getElementById('register-email').value = '';
                    document.getElementById('register-password').value = '';

                    // Auto switch to login form after 1.5 seconds
                    setTimeout(() => {
                        showLoginForm();
                        showStatus('auth-status', 'Please login with your new account', 'info');
                    }, 1500);
                } else {
                    // Show error message for failed registration
                    showStatus('auth-status', result.error || 'Registration failed, please try again', 'error');
                }
            } catch (error) {
                console.error('Registration error:', error);
                showStatus('auth-status', error.message || 'Registration failed, please try again', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('register-btn');
            }
        }

        async function login() {
            const email = document.getElementById('login-email').value.trim();
            const password = document.getElementById('login-password').value;

            if (!validateInput(email, 'email') || !validateInput(password, 'password')) {
                showStatus('auth-status', 'Please fill in all fields correctly', 'error');
                return;
            }

            // Show loading state
            LoadingManager.showButtonLoading('login-btn');
            showStatus('auth-status', 'Authenticating...', 'info');

            // Security: Check if account is locked
            if (isAccountLocked(email)) {
                showStatus('auth-status', 'Account temporarily locked due to too many failed attempts. Try again later.', 'error');
                LoadingManager.hideButtonLoading('login-btn');
                return;
            }

            try {
                const saltResponse = await apiManager.getSalt(email);
                const baseSalt = new Uint8Array(saltResponse.salt);
                const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
                const authKey = await CryptoUtils.deriveAuthKey(password, authSalt);
                const result = await apiManager.login(email, authKey);

                if (result.success) {
                    // Clear failed attempts on successful login
                    clearLoginAttempts(email);

                    // Set JWT token for API requests
                    apiManager.setSecureToken(result.token);

                    const encryptionSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'encryption');
                    encryptionKey = await CryptoUtils.deriveKey(password, encryptionSalt);
                    currentUser = email;
                    lastLoginAt = result.lastLoginAt;

                    // Current user email is already stored in memory variable

                    showStatus('auth-status', 'Login successful!', 'success');

                    // Clear password for security
                    document.getElementById('login-password').value = '';

                    setTimeout(() => {
                        showNotesSection();

                        // Check admin permissions
                        checkAdminStatus();
                    }, 1000);
                } else if (result.requires2FA) {
                    // 2FA required - show 2FA verification modal
                    LoadingManager.hideButtonLoading('login-btn');
                    show2FALoginModal(result.tempToken, email, password, baseSalt);
                    return;
                } else {
                    // Record failed attempt
                    recordFailedLogin(email);

                    // Show error message for failed login
                    showStatus('auth-status', result.error || 'Invalid email or password, please try again', 'error');
                }
            } catch (error) {
                // Record failed attempt for network/system errors
                recordFailedLogin(email);

                showStatus('auth-status', error.message || 'Login failed, please check network connection', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('login-btn');
            }
        }

        async function saveNote() {
            const content = document.getElementById('note-content').value.trim();

            if (!validateInput(content, 'note')) {
                if (!content) {
                    showStatus('notes-status', 'Please enter note content', 'error');
                } else if (content.length > 1000) {
                    showStatus('notes-status', 'Note content cannot exceed 1000 characters', 'error');
                } else {
                    showStatus('notes-status', 'Note content must be at least 1 character', 'error');
                }
                return;
            }

            if (!encryptionKey) {
                showStatus('notes-status', 'Invalid encryption key, please login again', 'error');
                return;
            }

            // Show loading state
            LoadingManager.showButtonLoading('save-note-btn');
            showStatus('notes-status', 'Encrypting and saving note...', 'info');

            try {
                // Generate preview title (1/3 of content, max 15 chars)
                const previewLength = Math.min(Math.floor(content.length / 3), 15);
                const previewTitle = content.substring(0, previewLength);

                const encryptedContent = await CryptoUtils.encryptText(content, encryptionKey);
                const encryptedPreviewTitle = await CryptoUtils.encryptText(previewTitle, encryptionKey);
                const result = await apiManager.saveNote(encryptedContent, encryptedPreviewTitle);

                if (result.success) {
                    document.getElementById('note-content').value = '';
                    showStatus('notes-status', 'Note saved successfully!', 'success');
                    loadNotes();
                } else {
                    showStatus('notes-status', result.error || 'Save failed, please try again', 'error');
                }
            } catch (error) {
                console.error('Save note error:', error);
                showStatus('notes-status', error.message || 'Save failed, please try again', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('save-note-btn');
            }
        }

        // Edit note
        async function editNote(noteId) {
            if (editingNoteId) {
                showStatus('notes-status', 'Please finish editing the current note first', 'error');
                return;
            }

            const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
            if (!noteElement) return;

            // Decrypt full content if not already decrypted
            let fullContent = decryptedNotes.get(noteId);
            if (!fullContent) {
                const note = allNotes.find(n => n.id == noteId);
                if (note) {
                    try {
                        fullContent = await CryptoUtils.decryptText(note.encrypted_content, encryptionKey);
                        decryptedNotes.set(noteId, fullContent);
                    } catch (error) {
                        showStatus('notes-status', 'Failed to decrypt note', 'error');
                        return;
                    }
                }
            }

            const contentElement = noteElement.querySelector('.note-content');

            // Hide expand button during edit
            const expandBtn = noteElement.querySelector('.note-expand-btn');
            if (expandBtn) {
                expandBtn.style.display = 'none';
            }

            // Create edit interface
            noteElement.classList.add('edit-mode');
            contentElement.innerHTML = `
                <textarea class="edit-textarea" id="edit-textarea-${noteId}" placeholder="Edit your note..." maxlength="1000">${escapeHTML(fullContent)}</textarea>
                <div class="edit-actions">
                    <div class="edit-char-counter" id="edit-char-counter-${noteId}">0/1000</div>
                    <button class="btn btn-secondary btn-small" id="cancel-edit-${noteId}">Cancel</button>
                    <button class="btn btn-small" id="save-edit-${noteId}">Save Changes</button>
                </div>
            `;

            // Bind events for edit buttons
            document.getElementById(`save-edit-${noteId}`).addEventListener('click', () => saveEditedNote(noteId));
            document.getElementById(`cancel-edit-${noteId}`).addEventListener('click', () => cancelEdit(noteId));

            // Focus on textarea and setup character counter
            const textarea = document.getElementById(`edit-textarea-${noteId}`);
            textarea.focus();
            textarea.setSelectionRange(textarea.value.length, textarea.value.length);

            // Initialize character counter
            updateCharCounter(textarea, `edit-char-counter-${noteId}`, 1000);

            // Add input event listener for character counter
            textarea.addEventListener('input', function() {
                updateCharCounter(this, `edit-char-counter-${noteId}`, 1000);
            });

            editingNoteId = noteId;
        }

        // Save edited note
        async function saveEditedNote(noteId) {
            const textarea = document.getElementById(`edit-textarea-${noteId}`);
            const newContent = textarea.value.trim();

            if (!validateInput(newContent, 'note')) {
                if (!newContent) {
                    showStatus('notes-status', 'Note content cannot be empty', 'error');
                } else if (newContent.length > 1000) {
                    showStatus('notes-status', 'Note content cannot exceed 1000 characters', 'error');
                } else {
                    showStatus('notes-status', 'Note content must be at least 1 character', 'error');
                }
                return;
            }

            if (newContent.length > CONFIG.MAX_NOTE_LENGTH) {
                showStatus('notes-status', `Note content cannot exceed ${CONFIG.MAX_NOTE_LENGTH} characters`, 'error');
                return;
            }

            // Show loading state for the save button
            const saveBtn = document.getElementById(`save-edit-${noteId}`);
            if (saveBtn) {
                saveBtn.classList.add('loading');
                saveBtn.disabled = true;
            }
            showStatus('notes-status', 'Updating note...', 'info');

            try {
                // Generate new preview title
                const previewLength = Math.min(Math.floor(newContent.length / 3), 15);
                const previewTitle = newContent.substring(0, previewLength);

                const encryptedContent = await CryptoUtils.encryptText(newContent, encryptionKey);
                const encryptedPreviewTitle = await CryptoUtils.encryptText(previewTitle, encryptionKey);
                const result = await apiManager.updateNote(noteId, encryptedContent, encryptedPreviewTitle);

                if (result.success) {
                    // Clear decrypted content from memory
                    decryptedNotes.delete(noteId);
                    showStatus('notes-status', 'Note updated successfully!', 'success');
                    loadNotes();
                    editingNoteId = null;
                } else {
                    showStatus('notes-status', result.error || 'Update failed, please try again', 'error');
                }
            } catch (error) {
                console.error('Update note error:', error);
                showStatus('notes-status', error.message || 'Update failed, please try again', 'error');
            } finally {
                // Reset button state
                if (saveBtn) {
                    saveBtn.classList.remove('loading');
                    saveBtn.disabled = false;
                }
            }
        }

        // Cancel edit
        function cancelEdit(noteId) {
            // Clear decrypted content from memory
            decryptedNotes.delete(noteId);
            editingNoteId = null;
            loadNotes(); // Reload notes to restore original content
        }

        // Delete note
        async function deleteNote(noteId) {
            if (!confirm('Are you sure you want to delete this note? This action cannot be undone!')) {
                return;
            }

            // Show loading state for the specific delete button
            const deleteBtn = document.querySelector(`[data-note-id="${noteId}"] .btn-danger`);
            if (deleteBtn) {
                deleteBtn.classList.add('loading');
                deleteBtn.disabled = true;
            }
            showStatus('notes-status', 'Deleting note...', 'info');

            try {
                const result = await apiManager.deleteNote(noteId);
                if (result.success) {
                    showStatus('notes-status', 'Note deleted successfully!', 'success');
                    loadNotes();
                } else {
                    showStatus('notes-status', result.error || 'Delete failed, please try again', 'error');
                }
            } catch (error) {
                console.error('Delete note error:', error);
                showStatus('notes-status', error.message || 'Delete failed, please try again', 'error');
            } finally {
                // Reset button state (will be replaced by loadNotes anyway)
                if (deleteBtn) {
                    deleteBtn.classList.remove('loading');
                    deleteBtn.disabled = false;
                }
            }
        }

        async function loadNotes(page = 1) {
            if (!currentUser || !encryptionKey) {
                return;
            }

            currentPage = page;

            // Show loading state
            LoadingManager.showLoading('load-notes');

            try {
                showStatus('notes-status', 'Loading notes...', 'info');
                allNotes = await apiManager.getNotes();
                const notesListEl = document.getElementById('notes-list');

                if (allNotes.length === 0) {
                    notesListEl.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">No notes yet, write your first one!</p>';
                    document.getElementById('pagination-controls').style.display = 'none';
                    showStatus('notes-status', '', 'info');
                    return;
                }

                // Calculate pagination
                const notesPerPage = 10;
                totalPages = Math.ceil(allNotes.length / notesPerPage);
                const startIndex = (currentPage - 1) * notesPerPage;
                const endIndex = startIndex + notesPerPage;
                const currentPageNotes = allNotes.slice(startIndex, endIndex);

                let notesHtml = `<h4>My Notes (${allNotes.length} items)</h4>`;

                for (const note of currentPageNotes) {
                    try {
                        const createdAt = new Date(note.created_at).toLocaleString('zh-CN');
                        let displayContent = '';
                        let hasExpandBtn = false;

                        // Check if note is currently being edited
                        if (editingNoteId == note.id) {
                            // Check if editing interface actually exists
                            const editingElement = document.getElementById(`edit-textarea-${note.id}`);
                            if (editingElement) {
                                // Skip rendering for editing note, it will be handled by editNote function
                                continue;
                            } else {
                                // Editing interface doesn't exist, clear the editing state
                                editingNoteId = null;
                            }
                        }

                        // Try to decrypt preview title first
                        if (note.encrypted_preview_title) {
                            try {
                                const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                                displayContent = escapeHTML(previewTitle) + '...';
                                hasExpandBtn = true;
                            } catch (error) {
                                displayContent = 'Unable to decrypt preview';
                            }
                        } else {
                            // Fallback for old notes without preview title
                            displayContent = 'Click to expand...';
                            hasExpandBtn = true;
                        }

                        notesHtml += `
                            <div class="note-item" data-note-id="${note.id}">
                                <div class="note-actions">
                                    <button class="btn btn-small edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                                    <button class="btn btn-danger btn-small delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
                                </div>
                                <div class="note-meta">Created: ${createdAt}</div>
                                <div class="note-content collapsed">${displayContent}</div>
                                ${hasExpandBtn ? '<button class="note-expand-btn" onclick="expandNote(' + note.id + ')">展开</button>' : ''}
                            </div>
                        `;
                    } catch (error) {
                        console.error('Failed to process note:', error);
                        notesHtml += `
                            <div class="note-item" style="border-left-color: #dc3545;">
                                <div class="note-meta">Failed to process note</div>
                                <div class="note-content" style="color: #dc3545;">Unable to process this note</div>
                            </div>
                        `;
                    }
                }

                notesListEl.innerHTML = notesHtml;
                updatePaginationControls();
                showStatus('notes-status', '', 'info');
            } catch (error) {
                console.error('Load notes error:', error);
                showStatus('notes-status', error.message || 'Failed to load notes', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideLoading('load-notes');
            }
        }

        async function clearAllNotes() {
            if (!currentUser) return;

            if (confirm('Are you sure you want to delete all notes? This action cannot be undone!')) {
                // Show loading state
                LoadingManager.showButtonLoading('clear-all-notes-btn');
                showStatus('notes-status', 'Deleting all notes...', 'info');

                try {
                    const result = await apiManager.clearAllNotes();
                    if (result.success) {
                        loadNotes();
                        showStatus('notes-status', result.message || 'All notes have been cleared', 'info');
                    } else {
                        showStatus('notes-status', result.error || 'Clear failed, please try again', 'error');
                    }
                } catch (error) {
                    console.error('Clear notes error:', error);
                    showStatus('notes-status', error.message || 'Clear failed, please try again', 'error');
                } finally {
                    // Hide loading state
                    LoadingManager.hideButtonLoading('clear-all-notes-btn');
                }
            }
        }

        function logout() {
            // Check if apiManager is initialized before calling logout
            if (typeof apiManager !== 'undefined' && apiManager) {
                apiManager.logout();
            }
            currentUser = null;
            encryptionKey = null;
            notes = [];
            editingNoteId = null;
            lastLoginAt = null;
            isAdmin = false;

            // Clear auto-logout timer
            if (autoLogoutTimer) {
                clearTimeout(autoLogoutTimer);
                autoLogoutTimer = null;
            }

            // Clear all session data (privacy mode compatible)
            clearSessionData();

            // Clear all forms and sensitive inputs
            const loginEmail = document.getElementById('login-email');
            const loginPassword = document.getElementById('login-password');
            const registerEmail = document.getElementById('register-email');
            const registerPassword = document.getElementById('register-password');
            const noteContent = document.getElementById('note-content');
            const currentPassword = document.getElementById('current-password');
            const newPassword = document.getElementById('new-password');
            const confirmPassword = document.getElementById('confirm-password');
            const sitePassword = document.getElementById('site-password');

            if (loginEmail) loginEmail.value = '';
            if (loginPassword) loginPassword.value = '';
            if (registerEmail) registerEmail.value = '';
            if (registerPassword) registerPassword.value = '';
            if (noteContent) noteContent.value = '';
            if (currentPassword) currentPassword.value = '';
            if (newPassword) newPassword.value = '';
            if (confirmPassword) confirmPassword.value = '';
            if (sitePassword) sitePassword.value = '';

            // Clear notes list display
            const notesList = document.getElementById('notes-list');
            if (notesList) notesList.innerHTML = '';

            // Clear any status messages
            const statusElements = ['login-status', 'register-status', 'settings-status', 'backup-status-display'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.innerHTML = '';
            });

            // Clear user lists in admin section
            const usersList = document.getElementById('users-list');
            if (usersList) usersList.innerHTML = '';

            // Reset account email display
            const accountEmail = document.getElementById('account-email');
            if (accountEmail) accountEmail.textContent = '';

            // Clear login attempts tracking
            loginAttempts.clear();

            // Hide all sections completely and clear all CSS classes
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'none';
            document.getElementById('notes-section').style.display = 'none';
            document.getElementById('admin-section').style.display = 'none';

            // Remove any CSS classes that might interfere with page display
            document.getElementById('notes-section').classList.remove('active');

            document.getElementById('main-app').classList.remove('show');
            document.getElementById('access-gate').style.display = 'none';

            // Hide admin elements
            hideAdminButton();

            // Show loading state and re-check access requirements
            document.getElementById('loading-state').style.display = 'block';

            // Re-check access requirements after logout
            setTimeout(() => {
                checkAccessRequirement();
            }, 100);
        }

        function showStatus(elementId, message, type = 'info') {
            const statusEl = document.getElementById(elementId);
            if (message) {
                const safeMessage = escapeHTML(message);
                statusEl.innerHTML = `<div class="status ${type}">${safeMessage}</div>`;
                if (type !== 'info' || message.includes('Loading')) {
                    setTimeout(() => {
                        statusEl.innerHTML = '';
                    }, 5000);
                }
            } else {
                statusEl.innerHTML = '';
            }
        }

        function showAuthSection() {
            // Hide all sections first
            document.getElementById('notes-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'none';
            document.getElementById('admin-section').style.display = 'none';

            // Remove any CSS classes that might interfere
            document.getElementById('notes-section').classList.remove('active');

            // Show auth section
            document.getElementById('auth-section').style.display = 'block';
        }

        function showNotesSection() {
            // Hide all sections first
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'none';
            document.getElementById('admin-section').style.display = 'none';

            // Remove any CSS classes that might interfere
            document.getElementById('notes-section').classList.remove('active');

            // Show notes section
            document.getElementById('notes-section').style.display = 'block';

            // Security: Start auto-logout timer after successful login
            startAutoLogoutTimer();

            loadNotes();
            updateLastLoginDisplay();
        }

        function updateLastLoginDisplay() {
            const lastLoginInfo = document.getElementById('last-login-info');
            if (lastLoginInfo && lastLoginAt) {
                try {
                    const loginDate = new Date(lastLoginAt);
                    const now = new Date();
                    const diffMs = now - loginDate;
                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                    let timeAgo;
                    if (diffDays === 0) {
                        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                        if (diffHours === 0) {
                            const diffMinutes = Math.floor(diffMs / (1000 * 60));
                            timeAgo = diffMinutes <= 1 ? 'just now' : `${diffMinutes} minutes ago`;
                        } else {
                            timeAgo = diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
                        }
                    } else if (diffDays === 1) {
                        timeAgo = 'yesterday';
                    } else if (diffDays < 7) {
                        timeAgo = `${diffDays} days ago`;
                    } else {
                        timeAgo = loginDate.toLocaleDateString();
                    }

                    lastLoginInfo.textContent = `Last login: ${timeAgo}`;
                } catch (error) {
                    lastLoginInfo.textContent = '';
                }
            } else if (lastLoginInfo) {
                lastLoginInfo.textContent = '';
            }
        }

        // Expand note content
        async function expandNote(noteId) {
            const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
            if (!noteElement) return;

            const contentElement = noteElement.querySelector('.note-content');
            const expandBtn = noteElement.querySelector('.note-expand-btn');

            // Check if already expanded
            if (!contentElement.classList.contains('collapsed')) {
                // Collapse
                collapseNote(noteId);
                return;
            }

            try {
                // Decrypt full content if not already decrypted
                let fullContent = decryptedNotes.get(noteId);
                if (!fullContent) {
                    const note = allNotes.find(n => n.id == noteId);
                    if (note) {
                        fullContent = await CryptoUtils.decryptText(note.encrypted_content, encryptionKey);
                        decryptedNotes.set(noteId, fullContent);
                    }
                }

                // Expand
                contentElement.classList.remove('collapsed');
                contentElement.innerHTML = escapeHTML(fullContent);
                expandBtn.textContent = '折叠';
                expandBtn.setAttribute('onclick', `collapseNote(${noteId})`);
            } catch (error) {
                showStatus('notes-status', 'Failed to decrypt note content', 'error');
            }
        }

        // Collapse note content
        async function collapseNote(noteId) {
            const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
            if (!noteElement) return;

            const contentElement = noteElement.querySelector('.note-content');
            const expandBtn = noteElement.querySelector('.note-expand-btn');

            // Clear decrypted content from memory
            decryptedNotes.delete(noteId);

            // Get preview title
            const note = allNotes.find(n => n.id == noteId);
            let displayContent = 'Click to expand...';

            if (note && note.encrypted_preview_title) {
                try {
                    const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                    displayContent = escapeHTML(previewTitle) + '...';
                } catch (error) {
                    displayContent = 'Unable to decrypt preview';
                }
            }

            // Collapse
            contentElement.classList.add('collapsed');
            contentElement.innerHTML = displayContent;
            expandBtn.textContent = '展开';
            expandBtn.setAttribute('onclick', `expandNote(${noteId})`);
        }

        // Update pagination controls
        function updatePaginationControls() {
            const paginationControls = document.getElementById('pagination-controls');
            const prevBtn = document.getElementById('prev-page-btn');
            const nextBtn = document.getElementById('next-page-btn');
            const pageInfo = document.getElementById('page-info');

            if (totalPages <= 1) {
                paginationControls.style.display = 'none';
                return;
            }

            paginationControls.style.display = 'block';
            pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }

        // Update character counter
        function updateCharCounter(input, counterId, maxLength) {
            const counter = document.getElementById(counterId);
            if (!counter) return;

            const currentLength = input.value.length;
            counter.textContent = `${currentLength}/${maxLength}`;

            // Update counter color based on usage
            counter.classList.remove('warning', 'error');
            if (currentLength > maxLength * 0.9) {
                counter.classList.add('error');
            } else if (currentLength > maxLength * 0.8) {
                counter.classList.add('warning');
            }
        }

        // Enhanced input validation
        function validateInput(value, type) {
            if (!value || typeof value !== 'string') return false;

            switch (type) {
                case 'email':
                    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
                    return emailRegex.test(value) && value.length <= 254;
                case 'password':
                    return value.length >= 8 && value.length <= 64;
                case 'note':
                    return value.length >= 1 && value.length <= 1000;
                default:
                    return value.trim().length > 0;
            }
        }

        // Search notes across all pages with scope selection
        async function searchNotes(keyword) {
            const notesListEl = document.getElementById('notes-list');
            const searchScope = document.getElementById('search-scope').value;

            if (!keyword.trim()) {
                // If search is empty, clear editing state and reload current page
                editingNoteId = null;
                loadNotes(currentPage);
                return;
            }

            // Clear any existing editing state when starting search
            editingNoteId = null;

            // Show search progress in title
            const scopeText = searchScope === 'title' ? 'titles' : 'content';
            notesListEl.innerHTML = `<h4 id="notes-title">🔍 Searching ${scopeText}... (0/${allNotes.length})</h4>`;

            // Hide pagination during search
            document.getElementById('pagination-controls').style.display = 'none';

            let matchingNotes = [];
            let searchedCount = 0;
            const keywordLower = keyword.toLowerCase();

            try {
                // Search through all notes based on selected scope
                for (const note of allNotes) {
                    searchedCount++;
                    let isMatch = false;

                    try {
                        if (searchScope === 'title') {
                            // Only search in preview titles
                            if (note.encrypted_preview_title) {
                                try {
                                    const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                                    if (previewTitle.toLowerCase().includes(keywordLower)) {
                                        isMatch = true;
                                    }
                                } catch (error) {
                                    // Skip preview title if can't decrypt
                                }
                            }
                        } else {
                            // Search in both preview title and full content
                            // First check preview title if available
                            if (note.encrypted_preview_title) {
                                try {
                                    const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                                    if (previewTitle.toLowerCase().includes(keywordLower)) {
                                        isMatch = true;
                                    }
                                } catch (error) {
                                    // Skip preview title if can't decrypt
                                }
                            }

                            // If not found in preview, search full content
                            if (!isMatch) {
                                const fullContent = await CryptoUtils.decryptText(note.encrypted_content, encryptionKey);
                                if (fullContent.toLowerCase().includes(keywordLower)) {
                                    isMatch = true;
                                }
                            }
                        }

                        if (isMatch) {
                            matchingNotes.push(note);
                        }
                    } catch (error) {
                        // Skip notes that can't be decrypted
                        console.error('Failed to decrypt note for search:', error);
                    }

                    // Update progress in title
                    if (searchedCount % 5 === 0 || searchedCount === allNotes.length) {
                        const titleEl = document.getElementById('notes-title');
                        if (titleEl) {
                            const percentage = Math.round((searchedCount / allNotes.length) * 100);
                            titleEl.textContent = `🔍 Searching ${scopeText}... (${searchedCount}/${allNotes.length} - ${percentage}% - Found: ${matchingNotes.length})`;
                        }
                        // Allow UI to update
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }

                // Display search results
                await displaySearchResults(matchingNotes, keyword, searchScope);

            } catch (error) {
                console.error('Search error:', error);
                notesListEl.innerHTML = `<h4>❌ Search Failed - Please try again</h4>`;
            }
        }

        // Display search results
        async function displaySearchResults(matchingNotes, keyword, searchScope) {
            const notesListEl = document.getElementById('notes-list');
            const scopeLabel = searchScope === 'title' ? 'titles' : 'content';

            // Update title with final results
            let notesHtml = '';
            if (matchingNotes.length === 0) {
                notesHtml = `<h4>🔍 No results found in ${scopeLabel} for "${escapeHTML(keyword)}" (searched ${allNotes.length} notes)</h4>`;
            } else {
                notesHtml = `<h4>🔍 Found ${matchingNotes.length} result(s) in ${scopeLabel} for "${escapeHTML(keyword)}"</h4>`;

                // Add the matching notes
                for (const note of matchingNotes) {
                    try {
                        const createdAt = new Date(note.created_at).toLocaleString('zh-CN');
                        let displayContent = '';
                        let hasExpandBtn = false;

                        // Try to decrypt preview title first
                        if (note.encrypted_preview_title) {
                            try {
                                const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                                displayContent = escapeHTML(previewTitle) + '...';
                                hasExpandBtn = true;
                            } catch (error) {
                                displayContent = 'Unable to decrypt preview';
                            }
                        } else {
                            displayContent = 'Click to expand...';
                            hasExpandBtn = true;
                        }

                        notesHtml += `
                            <div class="note-item" data-note-id="${note.id}">
                                <div class="note-actions">
                                    <button class="btn btn-small edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                                    <button class="btn btn-danger btn-small delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
                                </div>
                                <div class="note-meta">Created: ${createdAt}</div>
                                <div class="note-content collapsed">${displayContent}</div>
                                ${hasExpandBtn ? '<button class="note-expand-btn" onclick="expandNote(' + note.id + ')">展开</button>' : ''}
                            </div>
                        `;
                    } catch (error) {
                        console.error('Failed to process search result:', error);
                    }
                }
            }

            notesListEl.innerHTML = notesHtml;
            // Hide pagination during search
            document.getElementById('pagination-controls').style.display = 'none';
        }

        // Auth form switching functions
        function showLoginForm() {
            document.getElementById('login-form').classList.remove('hidden');
            document.getElementById('register-form').classList.add('hidden');
            // Clear any existing status messages
            showStatus('auth-status', '', 'info');
        }

        function showRegisterForm() {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('register-form').classList.remove('hidden');
            // Clear any existing status messages
            showStatus('auth-status', '', 'info');
        }

        /**
         * Settings page functions
         */
        function showSettingsPage() {
            // Hide all sections first
            document.getElementById('notes-section').style.display = 'none';
            document.getElementById('admin-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'block';

            // Display current user email
            if (currentUser) {
                document.getElementById('account-email').textContent = currentUser;
            }

            // Clear any previous status messages
            showStatus('settings-status', '', 'info');

            // Load 2FA status
            load2FAStatus();
        }

        function showNotesPage() {
            // Hide all sections first
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('settings-section').style.display = 'none';
            document.getElementById('admin-section').style.display = 'none';

            // Remove any CSS classes that might interfere
            document.getElementById('notes-section').classList.remove('active');

            // Show notes section
            document.getElementById('notes-section').style.display = 'block';
        }

        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const toggle = input.nextElementSibling;

            if (input.type === 'password') {
                input.type = 'text';
                toggle.textContent = '🙈';
            } else {
                input.type = 'password';
                toggle.textContent = '👁️';
            }
        }

        async function changePassword() {
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // Validation
            if (!currentPassword || !newPassword || !confirmPassword) {
                showStatus('settings-status', 'Please fill in all password fields', 'error');
                return;
            }

            if (newPassword.length < 8) {
                showStatus('settings-status', 'New password must be at least 8 characters long', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showStatus('settings-status', 'New passwords do not match', 'error');
                return;
            }

            if (currentPassword === newPassword) {
                showStatus('settings-status', 'New password must be different from current password', 'error');
                return;
            }

            // Show loading state
            LoadingManager.showButtonLoading('change-password-btn');
            showStatus('settings-status', 'Verifying current password and re-encrypting notes...', 'info');

            try {

                // First verify current password by trying to decrypt existing notes
                const saltResponse = await apiManager.getSalt(currentUser);
                if (!saltResponse.success) {
                    throw new Error('Failed to verify current password');
                }

                const baseSalt = new Uint8Array(saltResponse.salt);

                // Security: Validate salt integrity
                if (!baseSalt || baseSalt.length !== 16) {
                    throw new Error('Invalid salt received from server');
                }
                const currentEncryptionSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'encryption');
                const currentKey = await CryptoUtils.deriveKey(currentPassword, currentEncryptionSalt);

                // Get current notes from API for verification and re-encryption
                const currentNotes = await apiManager.getNotes();

                // Try to decrypt one note to verify current password
                if (currentNotes.length > 0) {
                    try {
                        await CryptoUtils.decryptText(currentNotes[0].encrypted_content, currentKey);
                    } catch (error) {
                        throw new Error('Current password is incorrect');
                    }
                }

                // Use derived salts for different purposes to ensure cryptographic security
                const encryptionSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'encryption');
                const newKey = await CryptoUtils.deriveKey(newPassword, encryptionSalt);

                // Re-encrypt all notes with new password
                const reencryptedNotes = [];
                for (const note of currentNotes) {
                    const decryptedContent = await CryptoUtils.decryptText(note.encrypted_content, currentKey);
                    const reencryptedContent = await CryptoUtils.encryptText(decryptedContent, newKey);
                    reencryptedNotes.push({
                        id: note.id,
                        encrypted_content: reencryptedContent
                    });
                }

                // Generate auth keys for backend verification (use separate auth salt)
                const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
                const currentAuthKey = await CryptoUtils.deriveAuthKey(currentPassword, authSalt);
                const newAuthKey = await CryptoUtils.deriveAuthKey(newPassword, authSalt);

                // Call backend API to change password (salt remains unchanged)
                const response = await apiManager.changePassword(currentAuthKey, newAuthKey, reencryptedNotes);

                if (response.success) {
                    // Update local encryption key with new password
                    const newEncryptionSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'encryption');
                    const newEncryptionKey = await CryptoUtils.deriveKey(newPassword, newEncryptionSalt);

                    // Validate new key before using it
                    if (!(await CryptoUtils.validateKeyStrength(newEncryptionKey))) {
                        throw new Error('New encryption key failed validation');
                    }

                    // Clear old encryption key from memory (best effort)
                    if (encryptionKey) {
                        try {
                            CryptoUtils.clearSensitiveData(await window.crypto.subtle.exportKey('raw', encryptionKey));
                        } catch (e) {
                            // Key export might fail, but that's okay
                        }
                    }

                    encryptionKey = newEncryptionKey;

                    // Clear password fields immediately for security
                    document.getElementById('current-password').value = '';
                    document.getElementById('new-password').value = '';
                    document.getElementById('confirm-password').value = '';

                    showStatus('settings-status', 'Password changed successfully! All notes have been re-encrypted with enhanced security.', 'success');

                    // Reload notes with new encryption to verify everything works
                    await loadNotes();
                } else {
                    throw new Error(response.error || 'Failed to change password');
                }

            } catch (error) {
                showStatus('settings-status', error.message || 'Failed to change password', 'error');
            } finally {
                // Hide loading state
                LoadingManager.hideButtonLoading('change-password-btn');
            }
        }

        // 2FA Functions
        let temp2FAToken = null;
        let temp2FACredentials = null;

        async function load2FAStatus() {
            try {
                const result = await apiManager.get2FAStatus();
                const statusEl = document.getElementById('2fa-status');
                const setupBtn = document.getElementById('2fa-setup-btn');
                const disableBtn = document.getElementById('2fa-disable-btn');
                const backupBtn = document.getElementById('2fa-backup-btn');

                // Check if elements exist before manipulating them
                if (!statusEl || !setupBtn || !disableBtn || !backupBtn) {
                    return; // Elements not found, probably not on settings page
                }

                if (result.success) {
                    if (result.enabled) {
                        statusEl.textContent = 'Enabled';
                        statusEl.className = 'status-indicator enabled';
                        setupBtn.style.display = 'none';
                        disableBtn.style.display = 'inline-block';
                        backupBtn.style.display = 'inline-block';
                    } else {
                        statusEl.textContent = 'Disabled';
                        statusEl.className = 'status-indicator disabled';
                        setupBtn.style.display = 'inline-block';
                        disableBtn.style.display = 'none';
                        backupBtn.style.display = 'none';
                    }
                } else {
                    statusEl.textContent = 'Error';
                    statusEl.className = 'status-indicator checking';
                }
            } catch (error) {
                console.error('Failed to load 2FA status:', error);
                const statusEl = document.getElementById('2fa-status');
                if (statusEl) {
                    statusEl.textContent = 'Error';
                    statusEl.className = 'status-indicator checking';
                }
            }
        }

        async function show2FASetupModal() {
            try {
                const modal = document.getElementById('2fa-setup-modal');
                const statusEl = document.getElementById('2fa-setup-status');

                // Clear previous status
                statusEl.innerHTML = '';

                // Show modal
                modal.style.display = 'flex';

                // Setup 2FA
                const result = await apiManager.setup2FA();
                if (result.success) {
                    // Display secret and QR code
                    document.getElementById('totp-secret').value = result.secret;

                    // Generate QR code directly in the display area
                    generateQRCode(result.qrCodeURL, 'qr-code-display');
                } else {
                    statusEl.innerHTML = `<div class="error">Failed to setup 2FA: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('2FA setup error:', error);
                document.getElementById('2fa-setup-status').innerHTML = `<div class="error">Setup failed: ${error.message}</div>`;
            }
        }

        async function verify2FASetup() {
            try {
                const code = document.getElementById('2fa-verify-code').value.trim();
                const statusEl = document.getElementById('2fa-setup-status');

                if (!/^\d{6}$/.test(code)) {
                    statusEl.innerHTML = '<div class="error">Please enter a valid 6-digit code</div>';
                    return;
                }

                LoadingManager.showButtonLoading('2fa-setup-verify');
                statusEl.innerHTML = '<div class="info">Verifying code...</div>';

                const result = await apiManager.verify2FA(code);

                if (result.success) {
                    statusEl.innerHTML = '<div class="success">2FA enabled successfully!</div>';

                    // Show backup codes
                    if (result.backupCodes) {
                        const backupCodesHtml = result.backupCodes.map(code =>
                            `<div class="backup-code-item">${code}</div>`
                        ).join('');

                        statusEl.innerHTML += `
                            <div class="backup-codes-display">
                                <h4>⚠️ Save these backup codes:</h4>
                                <div class="backup-codes-list">${backupCodesHtml}</div>
                                <p><strong>Important:</strong> Save these codes in a safe place. Each can only be used once.</p>
                            </div>
                        `;
                    }

                    setTimeout(() => {
                        hide2FASetupModal();
                        load2FAStatus();
                    }, 3000);
                } else {
                    statusEl.innerHTML = `<div class="error">Verification failed: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('2FA verification error:', error);
                document.getElementById('2fa-setup-status').innerHTML = `<div class="error">Verification failed: ${error.message}</div>`;
            } finally {
                LoadingManager.hideButtonLoading('2fa-setup-verify');
            }
        }

        function hide2FASetupModal() {
            document.getElementById('2fa-setup-modal').style.display = 'none';
            document.getElementById('2fa-verify-code').value = '';
            document.getElementById('2fa-setup-status').innerHTML = '';
        }

        function show2FALoginModal(tempToken, email, password, baseSalt) {
            temp2FAToken = tempToken;
            temp2FACredentials = { email, password, baseSalt };

            const modal = document.getElementById('2fa-login-modal');
            modal.style.display = 'flex';

            // Focus on the input
            setTimeout(() => {
                document.getElementById('2fa-login-code').focus();
            }, 100);
        }

        async function verify2FALogin() {
            try {
                const totpCode = document.getElementById('2fa-login-code').value.trim();
                const backupCode = document.getElementById('2fa-backup-code-login').value.trim();
                const statusEl = document.getElementById('2fa-login-status');

                if (!totpCode && !backupCode) {
                    statusEl.innerHTML = '<div class="error">Please enter a verification code or backup code</div>';
                    return;
                }

                if (totpCode && !/^\d{6}$/.test(totpCode)) {
                    statusEl.innerHTML = '<div class="error">Please enter a valid 6-digit code</div>';
                    return;
                }

                LoadingManager.showButtonLoading('2fa-login-verify');
                statusEl.innerHTML = '<div class="info">Verifying...</div>';

                // Check token status
                if (!temp2FAToken) {
                    statusEl.innerHTML = '<div class="error">Session expired. Please login again.</div>';
                    LoadingManager.hideButtonLoading('2fa-login-verify');
                    setTimeout(() => {
                        hide2FALoginModal();
                        showAuthSection();
                    }, 2000);
                    return;
                }

                // Check token expiration
                try {
                    const tokenParts = temp2FAToken.split('.');
                    if (tokenParts.length === 3) {
                        const payload = JSON.parse(atob(tokenParts[1].replace(/-/g, '+').replace(/_/g, '/')));
                        const currentTime = Math.floor(Date.now() / 1000);

                        if (payload.exp <= currentTime) {
                            statusEl.innerHTML = '<div class="error">Session expired. Please login again.</div>';
                            LoadingManager.hideButtonLoading('2fa-login-verify');
                            setTimeout(() => {
                                hide2FALoginModal();
                                showAuthSection();
                            }, 2000);
                            return;
                        }
                    }
                } catch (e) {
                    // Token format error, continue with request
                }

                const result = await apiManager.verify2FALogin(temp2FAToken, totpCode, backupCode);

                if (result.success) {
                    // Clear failed attempts on successful login
                    clearLoginAttempts(temp2FACredentials.email);

                    // Set JWT token for API requests
                    apiManager.setSecureToken(result.token);

                    const encryptionSalt = await CryptoUtils.deriveSubSalt(temp2FACredentials.baseSalt, 'encryption');
                    encryptionKey = await CryptoUtils.deriveKey(temp2FACredentials.password, encryptionSalt);
                    currentUser = temp2FACredentials.email;
                    lastLoginAt = result.lastLoginAt;

                    statusEl.innerHTML = '<div class="success">Login successful!</div>';

                    // Clear password for security
                    document.getElementById('login-password').value = '';

                    setTimeout(() => {
                        hide2FALoginModal();
                        showNotesSection();
                        checkAdminStatus();
                    }, 1000);
                } else {
                    // Handle specific error cases
                    if (result.error === 'Invalid or expired temporary token') {
                        statusEl.innerHTML = '<div class="error">Session expired. Please login again.</div>';
                        setTimeout(() => {
                            hide2FALoginModal();
                            showAuthSection();
                        }, 2000);
                    } else {
                        // Record failed attempt for other errors
                        recordFailedLogin(temp2FACredentials.email);
                        statusEl.innerHTML = `<div class="error">Verification failed: ${result.error}</div>`;
                    }
                }
            } catch (error) {
                document.getElementById('2fa-login-status').innerHTML = '<div class="error">Verification failed. Please try again.</div>';
                recordFailedLogin(temp2FACredentials.email);
            } finally {
                LoadingManager.hideButtonLoading('2fa-login-verify');
            }
        }

        function hide2FALoginModal() {
            document.getElementById('2fa-login-modal').style.display = 'none';
            document.getElementById('2fa-login-code').value = '';
            document.getElementById('2fa-backup-code-login').value = '';
            document.getElementById('2fa-login-status').innerHTML = '';
            document.getElementById('backup-code-input').style.display = 'none';
            temp2FAToken = null;
            temp2FACredentials = null;
        }

        function show2FABackupCodeInput() {
            document.getElementById('backup-code-input').style.display = 'block';
            document.getElementById('2fa-backup-code-login').focus();
        }

        async function show2FADisableModal() {
            const modal = document.getElementById('2fa-disable-modal');
            modal.style.display = 'flex';
        }

        async function disable2FA() {
            try {
                const code = document.getElementById('2fa-disable-code').value.trim();
                const statusEl = document.getElementById('2fa-disable-status');

                if (!/^\d{6}$/.test(code)) {
                    statusEl.innerHTML = '<div class="error">Please enter a valid 6-digit code</div>';
                    return;
                }

                LoadingManager.showButtonLoading('2fa-disable-confirm');
                statusEl.innerHTML = '<div class="info">Disabling 2FA...</div>';

                const result = await apiManager.disable2FA(code);

                if (result.success) {
                    statusEl.innerHTML = '<div class="success">2FA disabled successfully!</div>';
                    setTimeout(() => {
                        hide2FADisableModal();
                        load2FAStatus();
                    }, 2000);
                } else {
                    statusEl.innerHTML = `<div class="error">Failed to disable 2FA: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('2FA disable error:', error);
                document.getElementById('2fa-disable-status').innerHTML = `<div class="error">Failed to disable 2FA: ${error.message}</div>`;
            } finally {
                LoadingManager.hideButtonLoading('2fa-disable-confirm');
            }
        }

        function hide2FADisableModal() {
            document.getElementById('2fa-disable-modal').style.display = 'none';
            document.getElementById('2fa-disable-code').value = '';
            document.getElementById('2fa-disable-status').innerHTML = '';
        }

        async function show2FABackupModal() {
            const modal = document.getElementById('2fa-backup-modal');
            modal.style.display = 'flex';
        }

        async function generate2FABackupCodes() {
            try {
                const code = document.getElementById('2fa-backup-verify-code').value.trim();
                const statusEl = document.getElementById('2fa-backup-status');

                if (!/^\d{6}$/.test(code)) {
                    statusEl.innerHTML = '<div class="error">Please enter a valid 6-digit code</div>';
                    return;
                }

                LoadingManager.showButtonLoading('2fa-backup-generate');
                statusEl.innerHTML = '<div class="info">Generating backup codes...</div>';

                const result = await apiManager.generate2FABackupCodes(code);

                if (result.success) {
                    const backupCodesHtml = result.backupCodes.map(code =>
                        `<div class="backup-code-item">${code}</div>`
                    ).join('');

                    document.getElementById('backup-codes-display').style.display = 'block';
                    document.querySelector('.backup-codes-list').innerHTML = backupCodesHtml;
                    statusEl.innerHTML = '<div class="success">New backup codes generated!</div>';

                    // Store codes for download/print
                    window.currentBackupCodes = result.backupCodes;
                } else {
                    statusEl.innerHTML = `<div class="error">Failed to generate backup codes: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('2FA backup codes error:', error);
                document.getElementById('2fa-backup-status').innerHTML = `<div class="error">Failed to generate backup codes: ${error.message}</div>`;
            } finally {
                LoadingManager.hideButtonLoading('2fa-backup-generate');
            }
        }

        function hide2FABackupModal() {
            document.getElementById('2fa-backup-modal').style.display = 'none';
            document.getElementById('2fa-backup-verify-code').value = '';
            document.getElementById('2fa-backup-status').innerHTML = '';
            document.getElementById('backup-codes-display').style.display = 'none';
            window.currentBackupCodes = null;
        }

        function downloadBackupCodes() {
            if (!window.currentBackupCodes) return;

            const content = `Secure Notes - 2FA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\n${window.currentBackupCodes.join('\n')}\n\nImportant: Each code can only be used once. Store these codes in a safe place.`;

            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `secure-notes-backup-codes-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function printBackupCodes() {
            if (!window.currentBackupCodes) return;

            const printContent = `
                <html>
                <head>
                    <title>Secure Notes - 2FA Backup Codes</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; }
                        h1 { color: #333; }
                        .codes { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin: 20px 0; }
                        .code { padding: 10px; border: 1px solid #ccc; font-family: monospace; font-weight: bold; }
                        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>Secure Notes - 2FA Backup Codes</h1>
                    <p>Generated: ${new Date().toLocaleString()}</p>
                    <div class="codes">
                        ${window.currentBackupCodes.map(code => `<div class="code">${code}</div>`).join('')}
                    </div>
                    <div class="warning">
                        <strong>Important:</strong> Each code can only be used once. Store these codes in a safe place.
                    </div>
                </body>
                </html>
            `;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();
        }

        // Extract secret from TOTP URL
        function extractSecretFromURL(totpURL) {
            try {
                const url = new URL(totpURL);
                const secret = url.searchParams.get('secret');
                if (secret) {
                    // Format secret with spaces for easier reading
                    return secret.replace(/(.{4})/g, '$1 ').trim();
                }
                return 'Secret not found';
            } catch (error) {
                // Fallback: try to extract using regex
                const match = totpURL.match(/secret=([^&]+)/);
                if (match && match[1]) {
                    return match[1].replace(/(.{4})/g, '$1 ').trim();
                }
                return 'Secret not found';
            }
        }

        // QR Code generator using qrcode.js library with Canvas method
        function generateQRCode(text, containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            try {
                // Check if QRCode library is available
                if (typeof QRCode === 'undefined') {
                    throw new Error('QRCode library not loaded');
                }

                // Create container with canvas element
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; border: 2px solid #28a745; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-size: 18px; margin-bottom: 15px; color: #28a745;">📱 Scan QR Code</div>
                        <div style="margin-bottom: 15px;">
                            <canvas id="qr-canvas-${containerId}" style="border: 1px solid #ddd; border-radius: 8px; background: white; padding: 10px;"></canvas>
                        </div>
                        <p style="margin-bottom: 15px; color: #333;">Scan this QR code with your authenticator app</p>
                        <button type="button" onclick="showManualSetup('${text}', '${containerId}')" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            📝 Manual Setup Instead
                        </button>
                    </div>
                `;

                // Generate QR code using Canvas method
                const canvasElement = document.getElementById(`qr-canvas-${containerId}`);
                QRCode.toCanvas(canvasElement, text, {
                    width: 200,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error) {
                    if (error) {
                        // Fallback to manual setup
                        showManualSetup(text, containerId);
                    }
                });

            } catch (error) {
                console.error('QR Code generation failed:', error);
                // Fallback to manual setup
                showManualSetup(text, containerId);
            }
        }

        // Manual setup fallback function
        function showManualSetup(text, containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const secret = extractSecretFromURL(text);

            container.innerHTML = `
                <div style="text-align: center; padding: 20px; border: 2px solid #007bff; background: #f8f9fa; border-radius: 8px;">
                    <div style="font-size: 18px; margin-bottom: 15px; color: #007bff;">📱 Manual Setup</div>
                    <p style="margin-bottom: 15px; color: #333;">Please manually add this account to your authenticator app:</p>

                    <div style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 15px;">
                        <div style="margin-bottom: 10px;">
                            <strong>Account:</strong> Secure Notes (${getCurrentUserEmail()})
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>Secret Key:</strong>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 16px; font-weight: bold; letter-spacing: 2px; word-break: break-all; margin-bottom: 10px;">
                            ${secret}
                        </div>
                        <button type="button" onclick="copySecretToClipboard('${secret.replace(/\s/g, '')}', this)" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            📋 Copy Secret
                        </button>
                    </div>

                    <div style="font-size: 14px; color: #666; text-align: left;">
                        <strong>Setup Instructions:</strong>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li>Open your authenticator app (Google Authenticator, Authy, etc.)</li>
                            <li>Choose "Add account" or "+" button</li>
                            <li>Select "Enter a setup key" or "Manual entry"</li>
                            <li>Enter the account name: <strong>Secure Notes</strong></li>
                            <li>Copy and paste the secret key above</li>
                            <li>Save the account</li>
                        </ol>
                    </div>

                    <button type="button" onclick="generateQRCode('${text}', '${containerId}')" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px; margin-top: 10px;">
                        🔄 Try QR Code Again
                    </button>
                </div>
            `;
        }

        // Get current user email for display
        function getCurrentUserEmail() {
            return currentUser || 'your-email';
        }

        // Copy secret to clipboard
        function copySecretToClipboard(secret, buttonElement) {
            // If buttonElement is not provided, try to get it from event
            if (!buttonElement && typeof event !== 'undefined') {
                buttonElement = event.target;
            }

            navigator.clipboard.writeText(secret).then(() => {
                // Show feedback
                if (buttonElement) {
                    const originalText = buttonElement.textContent;
                    buttonElement.textContent = '✅ Copied!';
                    buttonElement.style.background = '#28a745';
                    setTimeout(() => {
                        buttonElement.textContent = originalText;
                        buttonElement.style.background = '#007bff';
                    }, 2000);
                }
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = secret;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                if (buttonElement) {
                    const originalText = buttonElement.textContent;
                    buttonElement.textContent = '✅ Copied!';
                    buttonElement.style.background = '#28a745';
                    setTimeout(() => {
                        buttonElement.textContent = originalText;
                        buttonElement.style.background = '#007bff';
                    }, 2000);
                }
            });
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);
            document.execCommand('copy');

            // Show feedback
            const originalValue = element.value;
            element.value = 'Copied!';
            setTimeout(() => {
                element.value = originalValue;
            }, 1000);
        }

        // Note: Using enhanced validateInput function defined above instead of duplicate validation functions

        // Initialization after page load
        document.addEventListener('DOMContentLoaded', function() {
            // Security: Verify configuration integrity
            verifyConfigIntegrity();

            // If URL contains accessToken, skip access gate and enter the main app
            const params = new URLSearchParams(window.location.search);
            const urlAccessToken = params.get('accessToken');
            if (urlAccessToken) {
                accessToken = urlAccessToken;
                accessTokenExpiry = Date.now() + (2 * 60 * 60 * 1000); // 2 hours
                sessionData.siteAccessVerified = true;
                sessionData.accessTimestamp = Date.now();
                sessionData.accessExpiry = accessTokenExpiry;

                // Show main app and bypass Access Gate
                document.getElementById('loading-state').style.display = 'none';
                document.getElementById('access-gate').style.display = 'none';
                document.getElementById('main-app').classList.add('show');

                if (currentUser && apiManager && apiManager.isTokenValid()) {
                    showNotesSection();
                    checkAdminStatus();
                } else {
                    showAuthSection();
                }

                // Start auto-logout timer
                startAutoLogoutTimer();

                try {
                    const cleanUrl = window.location.pathname + (window.location.hash || '');
                    history.replaceState(null, '', cleanUrl);
                } catch (e) {
                    // Ignore if history API is not available
                }

            } else {
                // Fallback to original access requirement check
                checkAccessRequirement();
            }

            // Security: Add activity listeners for auto-logout
            const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
            activityEvents.forEach(event => {
                document.addEventListener(event, resetAutoLogoutTimer, true);
            });

            // Bind button click events
            document.getElementById('login-btn').addEventListener('click', login);
            document.getElementById('register-btn').addEventListener('click', register);
            document.getElementById('verify-access-btn').addEventListener('click', verifySiteAccess);
            document.getElementById('logout-btn').addEventListener('click', logout);
            document.getElementById('save-note-btn').addEventListener('click', saveNote);
            document.getElementById('clear-all-notes-btn').addEventListener('click', clearAllNotes);

            // Bind admin buttons
            document.getElementById('admin-btn').addEventListener('click', showAdminSection);
            document.getElementById('back-to-notes-btn').addEventListener('click', hideAdminSection);
            document.getElementById('backup-status-btn').addEventListener('click', loadBackupStatus);
            document.getElementById('manual-backup-btn').addEventListener('click', triggerManualBackup);
            document.getElementById('restore-backup-btn').addEventListener('click', showRestoreBackupModal);
            document.getElementById('refresh-users-btn').addEventListener('click', loadUsers);

            // Bind restore modal buttons
            document.getElementById('restore-modal-close').addEventListener('click', hideRestoreBackupModal);
            document.getElementById('restore-cancel-btn').addEventListener('click', hideRestoreBackupModal);
            document.getElementById('restore-confirm-btn').addEventListener('click', confirmRestoreBackup);

            // Close modal when clicking outside
            document.getElementById('restore-backup-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideRestoreBackupModal();
                }
            });

            // Bind auth form switching events
            document.getElementById('show-register').addEventListener('click', showRegisterForm);
            document.getElementById('show-login').addEventListener('click', showLoginForm);

            // Settings page event listeners
            document.getElementById('settings-btn').addEventListener('click', showSettingsPage);
            document.getElementById('back-to-notes-from-settings-btn').addEventListener('click', showNotesPage);
            document.getElementById('change-password-btn').addEventListener('click', changePassword);

            // 2FA event listeners - with null checks
            const elements2FA = [
                { id: '2fa-setup-verify', event: 'click', handler: verify2FASetup },
                { id: '2fa-setup-cancel', event: 'click', handler: hide2FASetupModal },
                { id: '2fa-setup-close', event: 'click', handler: hide2FASetupModal },
                { id: '2fa-login-verify', event: 'click', handler: verify2FALogin },
                { id: '2fa-login-cancel', event: 'click', handler: hide2FALoginModal },
                { id: '2fa-disable-cancel', event: 'click', handler: hide2FADisableModal },
                { id: '2fa-disable-close', event: 'click', handler: hide2FADisableModal },
                { id: '2fa-backup-cancel', event: 'click', handler: hide2FABackupModal },
                { id: '2fa-backup-close', event: 'click', handler: hide2FABackupModal }
            ];

            elements2FA.forEach(item => {
                const element = document.getElementById(item.id);
                if (element) {
                    element.addEventListener(item.event, item.handler);
                }
            });

            // Bind access password input enter event
            document.getElementById('site-password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    verifySiteAccess();
                }
            });

            // Bind login form enter key events
            document.getElementById('login-email').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('login-password').focus();
                }
            });

            document.getElementById('login-password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    login();
                }
            });

            // Bind registration form enter key events
            document.getElementById('register-email').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('register-password').focus();
                }
            });

            document.getElementById('register-password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    register();
                }
            });

            // Add password strength indicator for register password
            document.getElementById('register-password').addEventListener('input', function(e) {
                const hintElement = document.getElementById('password-strength-hint');
                if (e.target.value.length > 0) {
                    hintElement.style.display = 'block';
                    updatePasswordStrengthIndicator(e.target.value);
                } else {
                    hintElement.style.display = 'none';
                }
            });

            // Add character counter for note content
            document.getElementById('note-content').addEventListener('input', function(e) {
                updateCharCounter(e.target, 'note-char-counter', 1000);
            });

            // Add search functionality with debouncing
            const searchInput = document.getElementById('note-search');
            const searchClearBtn = document.getElementById('search-clear-btn');
            const searchScope = document.getElementById('search-scope');
            let searchTimeout;

            // Search input with debouncing
            searchInput.addEventListener('input', function(e) {
                const keyword = e.target.value.trim();
                // Show/hide clear button
                searchClearBtn.style.display = keyword ? 'flex' : 'none';

                // Clear previous timeout
                clearTimeout(searchTimeout);

                if (keyword.length >= 1 || keyword.length === 0) {
                    // Use different debounce delays based on search scope
                    const debounceDelay = searchScope.value === 'title' ? 200 : 400;

                    searchTimeout = setTimeout(() => {
                        searchNotes(keyword);
                    }, debounceDelay);
                }
            });

            // Search scope change triggers immediate search if there's a keyword
            searchScope.addEventListener('change', function() {
                const keyword = searchInput.value.trim();
                if (keyword.length >= 1) {
                    clearTimeout(searchTimeout);
                    searchNotes(keyword);
                }
            });

            // Clear search functionality
            searchClearBtn.addEventListener('click', function() {
                searchInput.value = '';
                searchClearBtn.style.display = 'none';
                clearTimeout(searchTimeout);
                searchNotes(''); // Clear search results
                searchInput.focus();
            });

            // Add pagination event listeners
            document.getElementById('prev-page-btn').addEventListener('click', function() {
                if (currentPage > 1) {
                    loadNotes(currentPage - 1);
                }
            });

            document.getElementById('next-page-btn').addEventListener('click', function() {
                if (currentPage < totalPages) {
                    loadNotes(currentPage + 1);
                }
            });

            // Show password hint when register password field is focused
            document.getElementById('register-password').addEventListener('focus', function(e) {
                const hintElement = document.getElementById('password-strength-hint');
                if (e.target.value.length > 0) {
                    hintElement.style.display = 'block';
                    updatePasswordStrengthIndicator(e.target.value);
                }
            });

            // Bind 2FA form enter key events - with null checks
            const keyEvents2FA = [
                { id: '2fa-verify-code', handler: verify2FASetup },
                { id: '2fa-login-code', handler: verify2FALogin },
                { id: '2fa-backup-code-login', handler: verify2FALogin },
                { id: '2fa-disable-code', handler: disable2FA },
                { id: '2fa-backup-verify-code', handler: generate2FABackupCodes }
            ];

            keyEvents2FA.forEach(item => {
                const element = document.getElementById(item.id);
                if (element) {
                    element.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            item.handler();
                        }
                    });
                }
            });

            // Note: Removed enter key binding for note content to prevent accidental operations
            // Users can use the Save Note button or Ctrl+S shortcut
            document.getElementById('note-content').addEventListener('keydown', function(e) {
                if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    saveNote();
                }
            });

            // Event delegation for note actions (edit/delete buttons)
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('edit-note-btn')) {
                    const noteId = e.target.getAttribute('data-note-id');
                    editNote(noteId);
                } else if (e.target.classList.contains('delete-note-btn')) {
                    const noteId = e.target.getAttribute('data-note-id');
                    deleteNote(noteId);
                }
            });

            // Add character count functionality
            document.getElementById('note-content').addEventListener('input', function(e) {
                const length = e.target.value.length;
                const maxLength = CONFIG.MAX_NOTE_LENGTH;
                if (length > maxLength * 0.9) {
                    showStatus('notes-status', `Character count: ${length}/${maxLength}`, 'info');
                }
            });

        });

        // Error handling
        window.addEventListener('error', function(e) {
            showStatus('notes-status', 'An error occurred, please refresh the page and try again', 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            showStatus('notes-status', 'An error occurred, please try again', 'error');
        });
    </script>
</body>
</html>